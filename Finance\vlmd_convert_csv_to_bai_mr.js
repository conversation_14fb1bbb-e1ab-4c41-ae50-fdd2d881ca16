/**
 * @description Map/Reduce script to convert a CSV file to a BAI format file
 * 
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 */
define(['N/file', 'N/runtime', 'N/sftp', 'N/email', 'N/log', '../Classes/vlmd_convert_to_bai'], 
function(file, runtime, sftp, email, log, BaiConverter) {
  
  const SFTP_CONFIG = {
    username: "nsar",
    passwordGuid: "3b9e672d8a1d46139c9438bd61318684",
    url: "************",
    hostKey: "AAAAB3NzaC1yc2EAAAABIwAAAIEA2YIvZPcsWA3jP6yq5J" +
            "5XYunNySNspUZUhtzxXoePDZEhxJFIF/am9o+15CqO4hlyEDni0m+VW4" +
            "LVSoiUnj31xJis91EcYrUeCewIcSwof4Xs05ghth/2SKn0cITsR5hOeST" +
            "l4jFsMf9oK0+b2gr4jdV4fria4E/eF2u0qQArWuc=",
    port: 22
  };
  
  const DEFAULT_OWNER_ID = 15131;
  const TIME_THRESHOLD_MINUTES = 60;
  
  const baiConverter = new BaiConverter(SFTP_CONFIG, TIME_THRESHOLD_MINUTES);

  // Add ConvertedCSV path
  baiConverter.PATHS.CONVERTED_CSV = "ConvertedCSV/";

  /**
   * Move a file from one SFTP directory to another
   * 
   * @param {Object} connection - SFTP connection
   * @param {String} sourceFileName - Name of the file to move
   * @param {String} sourceDir - Source directory
   * @param {String} destDir - Destination directory
   * @returns {Boolean} - True if successful, false otherwise
   */
  function moveFileToDirectory(connection, sourceFileName, sourceDir, destDir) {
    try {
      // Ensure destination directory exists
      try {
        connection.list({
          path: destDir
        });
      } catch (dirError) {
        // Directory doesn't exist, create it
        connection.makeDirectory({
          path: destDir
        });
        log.audit("Created Directory", `Created directory ${destDir}`);
      }
      
      // Move the file
      connection.move({
        from: sourceDir + sourceFileName,
        to: destDir + sourceFileName
      });
      
      log.audit("File Moved", `Moved ${sourceFileName} from ${sourceDir} to ${destDir}`);
      return true;
    } catch (error) {
      log.error("Error Moving File", {
        sourceFile: sourceDir + sourceFileName,
        destFile: destDir + sourceFileName,
        error: error
      });
      return false;
    }
  }

  /**
   * Get input data - list of CSV files in the ConvertToBAI folder
   * 
   * @param {Object} inputContext
   * @returns {Array|Object} Array of file objects to process or file content
   */
  function getInputData(inputContext) {
    try {
           
      const connection = baiConverter.createSftpConnection(baiConverter.PATHS.ROOT);
      let fileList = [];
      
      try {
        fileList = connection.list({
          path: baiConverter.PATHS.CONVERT_TO_BAI
        });
        
      } catch (listError) {
        log.error("Error listing files in ConvertToBAI", listError);
        return [];
      }
      
      const csvFiles = fileList.filter(fileInfo => 
        !fileInfo.directory && 
        fileInfo.name.toLowerCase().endsWith('.csv')
      );
      
      log.audit("CSV Files to Process", `Found ${csvFiles.length} CSV files to convert`);
      
      if (csvFiles.length === 0) {
        log.audit("No CSV Files", "No CSV files found to process");
        return [];
      }
      
      return csvFiles.map(file => ({
        name: file.name,
        path: baiConverter.PATHS.CONVERT_TO_BAI + file.name
      }));
    } catch (error) {
      log.error('Error in getInputData', {
        message: error.message,
        stack: error.stack
      });
      
      sendErrorNotification('Error in CSV to BAI Conversion (getInputData)', error);
      
      return [];
    }
  }

  /**
   * Process each CSV file
   * 
   * @param {Object} mapContext
   */
  function map(mapContext) {
    try {
      const fileInfo = JSON.parse(mapContext.value);
      log.debug("Processing file", fileInfo.name);
      
      const connection = baiConverter.createSftpConnection(baiConverter.PATHS.ROOT);
      
      const downloadedFile = connection.download({
        directory: baiConverter.PATHS.CONVERT_TO_BAI,
        filename: fileInfo.name
      });
      
      const csvContent = downloadedFile.getContents();
      
      const rows = baiConverter.parseCSV(csvContent);
      
      if (rows.length === 0) {
        log.error(`File ${fileInfo.name} contains no valid data rows`);
        return;
      }
      
      mapContext.write('fileMetadata', JSON.stringify({ 
        sourceFileName: fileInfo.name 
      }));
      
      // Write each row with the file name as the key
      rows.forEach(row => {
        mapContext.write(fileInfo.name, JSON.stringify(row));
      });
      
    } catch (error) {
      log.error('Error in map stage', {
        value: mapContext.value,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Collect all rows from each file and create the BAI content
   * 
   * @param {Object} reduceContext
   */
  function reduce(reduceContext) {
    try {
      if (reduceContext.key === 'fileMetadata') {
        reduceContext.values.forEach(value => {
          reduceContext.write('fileMetadata', value);
        });
        return;
      }
      
      const fileName = reduceContext.key;
      const allRows = [];
      
      reduceContext.values.forEach(value => {
        allRows.push(JSON.parse(value));
      });
      
      if (allRows.length === 0) {
        log.error(`No valid rows found for file ${fileName}`);
        return;
      }
      
      // Pass the fileName to formatBAIContent
      const baiContent = baiConverter.formatBAIContent(allRows, fileName);
      
      reduceContext.write('baiFile', JSON.stringify({
        fileName: fileName,
        content: baiContent
      }));
    } catch (error) {
      log.error('Error in reduce stage', {
        key: reduceContext.key,
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Summarize stage - create and upload the BAI file
   * 
   * @param {Object} summaryContext
   */
  function summarize(summaryContext) {
    let errorOccurred = false;
    let sourceFileNames = [];
    let baiFiles = [];
    
    try {
      // Collect all BAI files and metadata
      summaryContext.output.iterator().each(function(key, value) {
        if (key === 'fileMetadata') {
          const metadata = JSON.parse(value);
          sourceFileNames.push(metadata.sourceFileName);
        } else if (key === 'baiFile') {
          baiFiles.push(JSON.parse(value));
        }
        return true;
      });
      
      if (baiFiles.length === 0) {
        log.audit('No BAI Content', 'The script ran successfully but no BAI content was generated. This may indicate an issue with the CSV data format.');
        return;
      }
      
      const scriptObj = runtime.getCurrentScript();
      const ownerId = scriptObj.getParameter({
        name: 'custscript_csv_to_bai_owner_id'
      }) || DEFAULT_OWNER_ID;
      
      // Create SFTP connection for moving files
      const sftpConnection = baiConverter.createSftpConnection(baiConverter.PATHS.ROOT);
      
      // Process each BAI file
      baiFiles.forEach((baiFileData, index) => {
        try {
          // Extract the original filename without extension
          const originalName = baiFileData.fileName.replace(/\.[^/.]+$/, "");
          
          const baiFile = file.create({
            name: `${originalName}.bai`, // Use original filename with .bai extension
            fileType: file.Type.PLAINTEXT,
            contents: baiFileData.content
          });
          
          let connection;
          try {
            connection = baiConverter.createSftpConnection(baiConverter.PATHS.POPULAR_BANK);
            
            connection.upload({
              file: baiFile,
              replaceExisting: true
            });
            
            log.audit('BAI File Uploaded to SFTP', `File uploaded to ${baiConverter.PATHS.POPULAR_BANK} folder: ${originalName}.bai`);
            
            // Move the source CSV file to the ConvertedCSV folder
            const moveSuccess = moveFileToDirectory(
              sftpConnection, 
              baiFileData.fileName, 
              baiConverter.PATHS.CONVERT_TO_BAI, 
              baiConverter.PATHS.CONVERTED_CSV
            );
            
            if (!moveSuccess) {
              log.error("Failed to move CSV file", `Could not move ${baiFileData.fileName} to ConvertedCSV folder`);
            }
            
          } catch (uploadError) {
            errorOccurred = true;
            throw new Error(`Failed to upload BAI file: ${uploadError.message}`);
          }
          
          // Send success email for each file
          email.send({
            author: ownerId,
            recipients: [ownerId],
            subject: 'CSV to BAI Conversion Complete',
            body: `
              <h2>CSV to BAI Conversion Successful</h2>
              <p>Your CSV file has been successfully converted to BAI format and has been uploaded to the SFTP server in the ${baiConverter.PATHS.POPULAR_BANK} folder.</p>
              <p><strong>Source File:</strong> ${baiFileData.fileName}</p>
              <p><strong>BAI File:</strong> ${originalName}.bai</p>
              <p><strong>Completion Time:</strong> ${new Date().toISOString()}</p>
              <p>The original CSV file has been moved to the ${baiConverter.PATHS.CONVERTED_CSV} directory.</p>
            `,
            attachments: [baiFile]
          });
        } catch (fileError) {
          log.error(`Error processing BAI file for ${baiFileData.fileName}`, fileError);
          sendErrorNotification(`CSV to BAI Conversion Failed for ${baiFileData.fileName}`, fileError);
        }
      });
    } catch (error) {
      log.error('Error in summarize stage', {
        message: error.message,
        stack: error.stack
      });
      
      sendErrorNotification('CSV to BAI Conversion Failed', error);
    }
  }

  /**
   * Send error notification email
   * @param {string} subject - Email subject
   * @param {Error} error - Error object
   */
  function sendErrorNotification(subject, error) {
    try {
      const scriptObj = runtime.getCurrentScript();
      const ownerId = scriptObj.getParameter({
        name: 'custscript_csv_to_bai_owner_id'
      }) || DEFAULT_OWNER_ID;
      
      email.send({
        author: ownerId,
        recipients: [ownerId],
        subject: subject,
        body: `
          <h2>Error Details:</h2>
          <p><strong>Message:</strong> ${error.message}</p>
          <p><strong>Stack:</strong> <pre>${error.stack || 'No stack trace available'}</pre></p>
          <p><strong>Script:</strong> ${scriptObj.id}</p>
          <p><strong>Deployment:</strong> ${scriptObj.deploymentId}</p>
          <p><strong>Time:</strong> ${new Date().toISOString()}</p>
        `
      });
    } catch (emailError) {
      log.error('Failed to send error notification', emailError);
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize
  };
});
