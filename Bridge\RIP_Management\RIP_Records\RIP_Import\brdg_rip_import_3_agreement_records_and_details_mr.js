/**
 * @description RIP Import 3:
 * Create Agreement and Agreement Details
 * from Bridge RIP Import records
 *
 * Submit the import agreement detail items MR once the agreement and agreement detail records
 *   have been created
 *
 * </br><b>Schedule:</b> On-demand, called by MR
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_import_3_agreement_records_and_details_mr
 */

define([
  "require",
  "BridgeHelperFunctionsLib",
  "Moment",
  "N/log",
  "N/format",
  "N/email",
  "N/runtime",
  "N/search",
  "N/record",
  "N/query",
  "N/task",
  "../../../../Helper_Libraries/vlmd_record_module_helper_lib",
  "../../../../Classes/vlmd_custom_error_object",
  "../../../../Classes/vlmd_mr_summary_handling",
], (
  /** @type {any} */ require,
  /** @type {import("../../../Libraries/brdg_helper_functions_lib")} */ bridgeHelperFunctionsLib,
  /** @type {any} */ moment
) => {
  const log = require("N/log");
  const format = require("N/format");
  const email = require("N/email");
  const runtime = require("N/runtime");
  const search = require("N/search");
  const record = require("N/record");
  const query = require("N/query");
  const task = require("N/task");

  /** @type {import("../../../../Helper_Libraries/vlmd_record_module_helper_lib")} */
  const recordHelperLib = require("../../../../Helper_Libraries/vlmd_record_module_helper_lib");

  /** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");
  const customErrorObject = new CustomErrorObject();

  const notificationAuthor = 223244; //<EMAIL>
  const maxNumberOfTiersPerGroup = 5; // Each Tier Group can have a maximum of 5 Tier Levels
  const maxErrorThreshold = 10;

  /**
	 * Get all inactive RIP records and return an object containing the tier group info and the the items in that tier
	 * consolidated by the rebate date range.
	 * 		Sample End Result:
					{
				 	  '3/01/2023_3/31/2023_1870': {
				     	'{"1":{"uom":1,"qty":5,"amt":10,"rip":123},"2":{"uom":2,"qty":10,"amt":20,"rip":123}}': [
								1,
								2,
								3,
							],
				 		'{"1":{"uom":1,"qty":8,"amt":2,"rip":800}': [4]
				 	  }
				 	}
					
	 * The key returned to map contains the start date, end date and the vendor ID
	 * The value is an object whose key is a stringified tier levels mapped to the array of items
	 *   that use the tier group and rip code
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
	 * @returns {{[key:string]: any}|undefined}
	 */
  function getInputData(context) {
    try {
      const currentScript = runtime.getCurrentScript();

      const runSpecificImportRecord = currentScript.getParameter({
        name: "custscript_specific_rip_id",
      });

      const specificItemId = currentScript.getParameter({
        name: "custscript_item_id_added_2",
      });
      // There are only 5 tiers, and the last is marked by the Tier 5 UOM column
      const finalTierUomIndex = 13;

      const /** @type {{[key:string]: any}} */ importedRipRecordsObj = {};
      const baseQuery = `SELECT
			id,
			custrecord_brdg_rip_import_uom_1,
			custrecord_brdg_rip_import_qty_1,
			custrecord_brdg_rip_import_amt_1,
			custrecord_brdg_rip_import_uom_2,
			custrecord_brdg_rip_import_qty_2,
			custrecord_brdg_rip_import_amt_2,
			custrecord_brdg_rip_import_uom_3,
			custrecord_brdg_rip_import_qty_3,
			custrecord_brdg_rip_import_amt_3,
			custrecord_brdg_rip_import_uom_4,
			custrecord_brdg_rip_import_qty_4,
			custrecord_brdg_rip_import_amt_4,
			custrecord_brdg_rip_import_uom_5,
			custrecord_brdg_rip_import_qty_5,
			custrecord_brdg_rip_import_amt_5,
			custrecord_brdg_rip_import_from,
			custrecord_brdg_rip_import_to,
			custrecord_brdg_rip_import_item,
			custrecord_brdg_rip_import_code,
			custrecord_brdg_rip_import_vendor
		FROM customrecord_brdg_rip_import
		WHERE isinactive = 'F'
	`;
      const additionalWhereClause = `AND custrecord_item_exists_in_ns = 'F' AND id = ${runSpecificImportRecord}`;
      const alreadyExists = ` and custrecord_item_exists_in_ns = 'T'`;
      query
        .runSuiteQLPaged({
          query:
            runSpecificImportRecord && runSpecificImportRecord != "null"
              ? `${baseQuery}${additionalWhereClause}`
              : `${baseQuery}${alreadyExists}`,
          pageSize: 1000,
        })
        .iterator()
        .each((page) => {
          //Build up the importedRipRecordsObj
          page.value.data.results.forEach((result) => {
            const from = result.values[16];
            const to = result.values[17];
            const itemSku = result.values[18];
            const ripCode = result.values[19];
            const vendorId = result.values[20];
            const agreementRecordCombination = `${from}_${to}_${vendorId}`;

            //Check if this combination exists in the importedRipRecordsObj. Add a new object to importedRipRecordsObj if not
            if (!importedRipRecordsObj[agreementRecordCombination]) {
              importedRipRecordsObj[agreementRecordCombination] = {};
            }

            /*Build up an object of tiers for this rip import record
							Sample End Result: 
									{
										1: {
											uom: 1,
											qty: 5,
											amt: 10,
											rip: 123
										},
										2: {
											uom: 2,
											qty: 10,
											amt: 20,
											rip: 123
										}
									}*/
            const /** @type {{[key:string]: any}} */ tiersObj = {};

            try {
              for (
                let newTierLevelIndex = 1, tierLevelsCounter = 1;
                newTierLevelIndex <= finalTierUomIndex
                  && tierLevelsCounter <= maxNumberOfTiersPerGroup;
                newTierLevelIndex += 3, tierLevelsCounter++
              ) {
                if (
                  result.values[newTierLevelIndex] &&
                  result.values[newTierLevelIndex + 1] &&
                  result.values[newTierLevelIndex + 2]
                ) {
                  tiersObj[tierLevelsCounter] = {
                    uom: /^(Bottles|Bottle\(s\))$/.test(result.values[newTierLevelIndex]?.toString() || "") ? 1 : 2,
                    qty: result.values[newTierLevelIndex + 1],
                    amt: result.values[newTierLevelIndex + 2],
                    rip: ripCode,
                  };
                }
              }
            } catch (/** @type {any} */ err) {
              throw customErrorObject.updateError({
                errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
                summary: "ERROR_SETTING_TIERS_OBJ",
                details: err.message,
              });
            }

            /* Convert the tiersObj to a JSON string
								Sample End Result: 
									'{"1":{"uom":1,"qty":5,"amt":10,"rip":123},"2":{"uom":2,"qty":10,"amt":20,"rip":123}}'*/
            const tiersJson = JSON.stringify(tiersObj);

            /*
						  Check if there's an property of this tier JSON beloning to an obj of this rebate agreement. 
						  If not, create the prop with a value of an array with this item in it. 
						  If yes, push the item into the existing array.
								Sample End Result: 
									{
										'3/01/2023_3/31/2023_1870': {
											'{"1":{"uom":1,"qty":5,"amt":10,"rip":123},"2":{"uom":2,"qty":10,"amt":20,"rip":123}}': [1,2,3],
											'{"1":{"uom":1,"qty":8,"amt":2,"rip":800}': [4]
										}
									}
						 */
            if (importedRipRecordsObj[agreementRecordCombination][tiersJson]) {
              importedRipRecordsObj[agreementRecordCombination][tiersJson].push(
                specificItemId && specificItemId != "null"
                  ? specificItemId
                  : itemSku
              );
            } else {
              importedRipRecordsObj[agreementRecordCombination][tiersJson] = [
                specificItemId && specificItemId != "null"
                  ? specificItemId
                  : itemSku,
              ];
            }
          });

          return true;
        });
      return importedRipRecordsObj;
    } catch (err) {
      customErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Create Agreement Records based on the start date, end date and vendor
   *
   * Pass the created Agreement Record ID to reduce context to reference the parent,
   *   and the month, year, RIP code, and vendor name to build the detail record name
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    const mapErrorObject = new CustomErrorObject();
    //Sample Context Key: 11/1/2023_11/30/2023_13366
    const [fromDateToFormat, toDateToFormat, vendorId] = context.key.split("_");
    let fromDate, toDate;
    try {
      if (!fromDateToFormat || !toDateToFormat || !vendorId) {
        throw mapErrorObject.updateError({
          errorType: mapErrorObject.ErrorTypes.MISSING_PARAM,
          summary: "MISSING_CONTEXT_KEY_VALUE",
          details: `fromDateToFormat : ${fromDateToFormat}, toDateToFormat : ${toDateToFormat}, vendorId : ${vendorId}`,
        });
      }

      const vendorName = search.lookupFields({
        type: search.Type.VENDOR,
        id: vendorId,
        columns: ["entityid"],
      })["entityid"]?.toString();

      const vendorNameAbbreviation = vendorName?.substring(0, 2).toUpperCase();

      const subsidiaries = bridgeHelperFunctionsLib.getBridgeSubsidiaries();

      fromDate = format.parse({
        value: fromDateToFormat,
        type: format.Type.DATE,
      });

      toDate = format.parse({
        value: toDateToFormat,
        type: format.Type.DATE,
      });

      const year = moment(fromDate).format("YYYY");
      const month = moment(fromDate).format("MM");
      const startDay = moment(fromDate).format("DD");
      const endDay = moment(toDate).format("DD");

      // E.g. ARAL2023030131 -> AR|AL|2023|03|01|31
      const rebateAgreementName = `AR${vendorNameAbbreviation}${year}${month}${startDay}${endDay}`;
      const mappedValuesObj = {
        name: rebateAgreementName,
        custrecord_rebate_vendor: vendorId,
        custrecord_rebate_subsidiaries: subsidiaries,
        custrecord_rebate_start_date: fromDate,
        custrecord_rebate_end_date: toDate,
      };
      let rebateAgreementId;
      const duplicateValidationQuery = `SELECT
            id
         FROM
            customrecord_rebate_agreement
         WHERE
            name = '${rebateAgreementName}'
            AND custrecord_rebate_vendor = '${vendorId}'
            AND BUILTIN.MNFILTER( custrecord_rebate_subsidiaries, 'MN_INCLUDE', '', 'FALSE', NULL, ${subsidiaries.join(
              ","
            )}) = 'T'
            AND custrecord_rebate_start_date = to_date('${moment(
              fromDate
            ).format("MM/DD/YYYY")}', 'MM/DD/YYYY')
            AND custrecord_rebate_end_date = to_date('${moment(toDate).format(
              "MM/DD/YYYY"
            )}', 'MM/DD/YYYY')`;

      const validateNotDuplicateQueryResults = query.runSuiteQL({
        query: duplicateValidationQuery,
      });

      //No agreement record with these details exists -> create a new one
      if (validateNotDuplicateQueryResults.results.length <= 0) {
        const agreementRecord = record.create({
          type: "customrecord_rebate_agreement",
          isDynamic: true,
        });

        try {
          // @ts-ignore Property 'setBodyValues' does not exist on type 'typeof import("vlmd_record_module_helper_lib")'.ts(2339)
          recordHelperLib.setBodyValues(mappedValuesObj, agreementRecord);
        } catch (/** @type {any} */ err) {
          throw mapErrorObject.updateError({
            errorType: mapErrorObject.ErrorTypes.VALUE_NOT_SET,
            summary: "ERROR_SETTING_AGREEMENT_RECORD_VALUES",
            details: err.message,
          });
        }

        rebateAgreementId = agreementRecord.save();
      } else {
        rebateAgreementId =
          validateNotDuplicateQueryResults.results[0].values[0];
      }

      if (!rebateAgreementId) {
        throw mapErrorObject.updateError({
          errorType: mapErrorObject.ErrorTypes.RECORD_NOT_SAVED,
          summary: "ERROR_SAVING_AGREEMENT_RECORD",
          details: `Rebate agreemeent record not saved successfully.`,
        });
      }

      /*We can't process the tier levels in map as the script will run out of usage 
					-> will use reduce to parse the stringified tier levels from the context value
				Write a JSON string of each tier group to context along with the items that belong to that tier group
						Sample Context Value Passed in from GetInputData: 
							{
								"{\"1\":{\"uom\":2,\"qty\":3,\"amt\":10,\"rip\":\"UPS\"},\"2\":{\"uom\":2,\"qty\":6,\"amt\":100,\"rip\":\"UPS\"},\"3\":{\"uom\":2,\"qty\":12,\"amt\":240,\"rip\":\"UPS\"}}": [
									38652,
									38652,
									38652,
									38652
									]
							}*/
      const parsedValue = JSON.parse(context.value);

      for (let tierLevels of Object.keys(parsedValue)) {
        /*The obj key -  "{\"1\":{\"uom\":2,\"qty\":5,\"amt\":50,\"rip\":\"90230\"}}"
				then parsed
				{
					"1": {
							"uom": 2,
							"qty": 5,
							"amt": 50,
							"rip": "90230"
						}
				}
				*/
        const parsedTierLevel = JSON.parse(tierLevels);

        /*Include the vendor id as a value in the obj to make the context key unique among each vendor.
				Will reduce the data by this unique JSON string*/
        parsedTierLevel["vendorId"] = vendorId;
        parsedTierLevel["rebateAgreementName"] = rebateAgreementName;
        context.write({
          // E.g. '{"1":{"uom":1,"qty":5,"amt":10,"rip":123},"2":{"uom":2,"qty":10,"amt":20,"rip":123},"vendorId":1870, "rebateAgreementName": "ARAL2023030131"}'
          key: JSON.stringify(parsedTierLevel),
          value: {
            month,
            year,
            items: parsedValue[tierLevels],
            vendorName,
            agreementId: rebateAgreementId,
          },
        });
      }
    } catch (/** @type {any} */err) {
      log.error("MAP", err);
      mapErrorObject.throwError({
        summaryText: `MAP_ERROR`,
        error: err,
        recordId: null,
        recordName: `fromDate : ${fromDate}, toDate : ${toDate}, vendorId : ${vendorId}`,
        recordType: "customrecord_rebate_agreement",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * Create the agreement detail record.
   * Parse the stringified tier levels
   * Lookup existing tier levels and tier groups
   * Create Agreement Detail records
   *
   * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
   * @returns {void}
   */
  function reduce(context) {
    const reduceErrorObject = new CustomErrorObject();
    const tierLevels = JSON.parse(context.key);

    try {
      if (context.values.length !== 1) {
        throw reduceErrorObject.updateError({
          errorType: reduceErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "ERROR_WITH_CONTEXT_VALUE",
          details: `Context value should have only 1 element. Value Passed In: ${context.values}`,
        });
      }

      const /** @type {any[]} */ tierLevelIds = [];
      let ripCode;

      //Build up object of tier level number and tierLevelRecordIds
      for (let i = 1; i <= maxNumberOfTiersPerGroup; i++) {
        if (tierLevels[i]) {
          const { uom, qty, amt, rip } = tierLevels[i];
          ripCode = rip;

          //Confirm active tier level record exist for this combination
          const tierLevelQuery = query.runSuiteQL({
            query: `
							SELECT id, name
							FROM customrecord_rebate_tier_level
							WHERE custrecord_unit_of_measure = ${uom}
							AND custrecord_tier_quantity = ${qty}
							AND custrecord_dollar_off = ${amt}
							AND isinactive = 'F'
						`,
          });

          //If exists, populate the tierLevelIds obj with the value of the int id for this tier level number
          if (tierLevelQuery.results.length > 0) {
            const tierLevelRecordId = tierLevelQuery.results[0].values[0];
            tierLevelIds[i] = tierLevelRecordId;
          }
        }
      }

      const tierFields = {
        1: "custrecord_tier_level_1",
        2: "custrecord_tier_level_2",
        3: "custrecord_tier_level_3",
        4: "custrecord_tier_level_4",
        5: "custrecord_tier_level_5",
      };

      const tierGroupQuery = `
			SELECT id, name
			FROM customrecord_rebate_tier_group
			WHERE ${bridgeHelperFunctionsLib.getTierFilters(tierLevelIds, tierFields)}
			AND isinactive = 'F'
		`;

      //Confirm active tier group exists for this combination
      const tierGroupQueryResults = query.runSuiteQL({
        query: tierGroupQuery,
      });

      //If tier group exists, get the tier group id and create an agreement detail record with all info needed
      if (tierGroupQueryResults.results.length > 0) {
        const tierGroupId = tierGroupQueryResults.results[0].values[0];
        const { month, year, items, vendorName, agreementId } = JSON.parse(
          context.values[0]
        );

        const vendorNameAbbreviation = vendorName.substring(0, 2).toUpperCase();

        const mappedValuesObj = {
          // E.g. ADAL202303R10 -> AD|AL|2023|03|R10
          name: `AD${vendorNameAbbreviation}${year}${month}R${ripCode}`,
          custrecord_rebate_items_included: items,
          custrecord_tier_group: tierGroupId,
          custrecord_rip_code: ripCode,
          custrecord_rebate_parent: agreementId,
        };
        const duplicateValidationQuery = `SELECT
                id
             FROM
                customrecord_rebate_agreement_detail
             WHERE
                name = '${mappedValuesObj.name}'
                AND BUILTIN.MNFILTER( custrecord_rebate_items_included, 'MN_INCLUDE', '', 'FALSE', NULL, 
                ${items.join(",")} ) = 'T'
                AND custrecord_tier_group = ${tierGroupId}
                AND custrecord_rip_code = '${ripCode}'
                AND custrecord_rebate_parent = ${agreementId}`;
        const validateNotDuplicateQueryResults = query.runSuiteQL({
          query: duplicateValidationQuery,
        });

        let rebateAgreementDetailId;

        //No agreement record with these details exists -> create a new one
        if (validateNotDuplicateQueryResults.results.length <= 0) {
          const agreementDetailRecord = record.create({
            type: "customrecord_rebate_agreement_detail",
            isDynamic: true,
          });

          try {
            // @ts-ignore Property 'setBodyValues' does not exist on type 'typeof import("vlmd_record_module_helper_lib")'.ts(2339)
            recordHelperLib.setBodyValues(
              mappedValuesObj,
              agreementDetailRecord
            );
          } catch (/** @type {any} */ err) {
            throw reduceErrorObject.updateError({
              errorType: reduceErrorObject.ErrorTypes.VALUE_NOT_SET,
              summary: "ERROR_SETTING_AGREEMENT_DETAIL_VALUES",
              details: err.message,
            });
          }
          rebateAgreementDetailId = agreementDetailRecord.save({
            enableSourcing: false,
            ignoreMandatoryFields: true,
          });
        } else {
          rebateAgreementDetailId =
            validateNotDuplicateQueryResults.results[0].values[0];
        }
        if (!rebateAgreementDetailId) {
          throw reduceErrorObject.updateError({
            errorType: reduceErrorObject.ErrorTypes.RECORD_NOT_SAVED,
            summary: "ERROR_SAVING_AGREEMENT_DETAIL_RECORD",
            details: `Rebate agreemeent details record not saved successfully.`,
          });
        }
        const currentScript = runtime.getCurrentScript();

        const specificRipRecordId = currentScript.getParameter({
          name: "custscript_specific_rip_id",
        });

        const specificItemId = currentScript.getParameter({
          name: "custscript_item_id_added_2",
        });

        if (
          specificItemId &&
          specificRipRecordId &&
          specificItemId != "null" &&
          specificRipRecordId != "null"
        ) {
          //If this is being called from the UE - that this was just ran for one item and one rip import record id
          record.submitFields({
            type: "customrecord_brdg_rip_import",
            id: specificRipRecordId.toString(),
            values: {
              custrecord_brdg_rip_import_item: specificItemId,
              custrecord_item_exists_in_ns: "T",
            },
            options: {
              enableSourcing: false,
              ignoreMandatoryFields: true,
            },
          });
        }
        context.write("Rebate Agreement Detail Id", rebateAgreementDetailId.toString());
      } else {
        throw reduceErrorObject.updateError({
          errorType: reduceErrorObject.ErrorTypes.MISSING_VALUE,
          summary: "TIER_GROUP_QUERY_ERROR",
          details: `No available tier group for the combination of tier levels.
QUERY: ${tierGroupQuery}`,
        });
      }
    } catch (/** @type {any} */err) {
      log.error("REDUCE", err);
      reduceErrorObject.throwError({
        summaryText: "REDUCE_ERROR",
        error: err,
        recordId: null,
        recordName: JSON.stringify(tierLevels),
        recordType: "customrecord_brdg_rip_import",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * The summarize stage of the Map/Reduce script.
   * Submits the RIP Cleanup Import MR
   * On Get Input Data Error,
   *   throw the error
   *   do not trigger the Cleanup MR
   *   send an email notification to the process owner
   * On Map Error,
   *   collate the agreement records from the RIP Import records into an HTML table
   *   error count <= 10 - send an email containing the table
   *   error count > 10 - send an email saying there's too many map stage errors
   * On Reduce Error,
   *   collate the agreement detail records into an HTML table
   *   error count <= 10 - send an email containing the table
   *   error count > 10 - send an email saying there's too many reduce stage errors
   *   trigger Cleanup MR if error count <= 10
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    let ownerId = 0;

    try {
      const currentScript = runtime.getCurrentScript();
      ownerId =
        Number(currentScript.getParameter({
          name: "custscript_brdg_rip_import_agrmnt_owner",
        }) || 0) ?? 3288;

      const /** @type {string[]} */ detailIds = [];
      context.output.iterator().each(function(key, value){
        detailIds.push(value);
        return true;
      });

      // Error in getInputData stage -> no records were processed -> stop execution.
      if (context.inputSummary.error) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_IN_GET_INPUT_DATA",
          details:
            "MR 3: Import process stopped because data to process wasn't loaded successfully.",
        });
      }

      const StageHandling = require("../../../../Classes/vlmd_mr_summary_handling");
      // @ts-ignore Type 'typeof import("vlmd_mr_summary_handling")' has no construct signatures.ts(2351)
      const stageHandling = new StageHandling(context);

      stageHandling.printScriptProcessingSummary();
      const { resultsLog, recordsProcessedMessage } =
        stageHandling.printRecordsProcessed();
      const { errorArr, errorsMessage } = stageHandling.printErrors({
        groupErrors: true,
        formatMessageForEmail: true,
      });

      // Too many errors, likely something wrong with the entire file -> stop execution.
      if (errorArr.length > maxErrorThreshold) {
        throw customErrorObject.updateError({
          errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "TOO_MANY_MAP_ERRORS",
          details: "Import process stopped because there were too many errors.",
        });
      }

      if (errorArr.length > 0) {
        email.send({
          author: notificationAuthor,
          recipients: [ownerId],
          subject: "Bridge RIP Import Agreements Error",
          body: `An error occurred while importing these rows from the RIP file. 
Please address errors below and create tier levels, tier groups, agreement records, agreement details manually\n\n
${errorsMessage}`,
        });
      }

      const messageFromPreviousStage = currentScript.getParameter({
        name: "custscript_brdg_rip_import_tiers_message",
      });

      const processingMessage = `${messageFromPreviousStage}
<b>Script #3: Create RIP Agreement and Agreement Details</b><br/>
${resultsLog.length} Rebate Agreement Detail${
        resultsLog.length == 1 ? "" : "s"
      } Created (internal ids):<br/>
${recordsProcessedMessage ? recordsProcessedMessage + "<br/><br/>" : ""}
${errorArr.length} Error${errorArr.length == 1 ? "" : "s"}${
        errorsMessage ? ": " + errorsMessage : "."
      }<br/><br/>`;

      const importCleanupMr = task.create({
        taskType: task.TaskType.MAP_REDUCE,
        scriptId: "customscript_brdg_rip_import_items_mr",
        deploymentId: "customdeploy_brdg_rip_import_items_mr",
        params: {
          custscript_brdg_rip_import_item_owner: ownerId || "",
          custscript_brdg_rip_agreement_message: processingMessage,
          custscript_brdg_rip_import_item_details: detailIds.join(","),
        },
      });

      importCleanupMr.submit();
    } catch (err) {
      email.send({
        author: notificationAuthor,
        recipients: [ownerId],
        subject: "Bridge RIP Import Agreements Error",
        body: `Import process stopped because there was an error while processing.<br><br>Please reach <NAME_EMAIL> for assistance.`,
      });

      customErrorObject.throwError({
        summaryText: `SUMMARIZE_ERROR`,
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
