/**
 * @description Parse the Incoming 810 File (Bill) from TrueCommerce
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR> <<EMAIL>>
 */

define([
  "require",
  "N/log",
  "N/runtime",
  "N/sftp",
  "../../Classes/Models/File/edi_file",
  "../../Classes/Models/File/edi_incoming",
  "../../Classes/Models/Partner/Vendor/edi_TrueCommerce",
  "../../Classes/Decorators/810/edi_810",
  "../../Classes/Decorators/810/edi_810_bill_parser",
  "../../Classes/Models/Server/edi_server",
  "../../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const runtime = require("N/runtime");
  const sftp = require("N/sftp");
  const { DocumentType } = require("../../Classes/Models/File/edi_file");
  const { EDIIncoming } = require("../../Classes/Models/File/edi_incoming");
  const {
    EDITrueCommerce,
  } = require("../../Classes/Models/Partner/Vendor/edi_TrueCommerce");
  const { EDI810 } = require("../../Classes/Decorators/810/edi_810");
  const {
    EDI810BillParser,
  } = require("../../Classes/Decorators/810/edi_810_bill_parser");
  const { EDIServer } = require("../../Classes/Models/Server/edi_server");
  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

  /**
   * Retrieve file names available from TrueCommerce
   *
   * @returns {string[]|undefined} File Names
   */
  function getInputData() {
    try {
      const truecommerce = new EDITrueCommerce({
        direction: "in",
        transactionType: "810",
      });
      const server = new EDIServer({
        partner: truecommerce,
        customError: new CustomErrorObject(),
      });
      const currentScript = runtime.getCurrentScript();
      const isUsingProdDirectory = currentScript.getParameter({
        name: "custscript_truecommerce_810_out_is_prod",
      });
      const isProdEnv =
        isUsingProdDirectory && runtime.envType === runtime.EnvType.PRODUCTION;

      log.audit({
        title: "EDI TrueCommerce 810 MR (getInputData): Is Prod?",
        details: isProdEnv,
      });

      server.connect({
        directory: truecommerce.prodDirectory,
        username: "FTPAdmin",
        passwordGuid: "ae98976727744c4db954694c6dd9f793",
      });

      if (server.connection) {
        return server.connection
          .list({
            path: "./",
            sort: sftp.Sort.NAME,
          })
          .map((file) => file.name)
          .filter((name) => ![".", ".."].includes(name));
      }

      return [];
    } catch (err) {
      log.error("err");
      new CustomErrorObject().throwError({
        summaryText: "GET_INPUT_DATA",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Parse and transform the relevant Purchase Order to a Bill
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    const filename = context.value;
    const currentScript = runtime.getCurrentScript();
    const isUsingProdDirectory = currentScript.getParameter({
      name: "custscript_truecommerce_810_in_is_prod",
    });

    log.audit({
      title: "EDI TrueCommerce 810 MR (map): File Name",
      details: filename,
    });

    const truecommerce = new EDITrueCommerce({
      direction: "in",
      transactionType: "810",
    });

    const ediFile = new EDIIncoming();

    ediFile.server = new EDIServer({
      partner: truecommerce,
      customError: ediFile.customError,
    });

    ediFile.server.connect({
      directory: truecommerce.prodDirectory,
      username: "FTPAdmin",
      passwordGuid: "ae98976727744c4db954694c6dd9f793",
    });

    const ediFileContent = ediFile.server.download(filename, { decode: true });

    if (ediFileContent) {
      const edi810BillParser = new EDI810BillParser({
        ediFileContent,
        filename,
        partnerValues: truecommerce.getParsingInformation({
          delimiters: truecommerce.delimiters,
          direction: "in",
        }),
        vendor: truecommerce,
      });

      new EDI810({
        parser: edi810BillParser,
        type: "Bill",
        typeId: DocumentType.INCOMING_810,
      }).decorate(ediFile);

      if (!isBilledToValmarSurgicalSupplies(ediFileContent)) {
        ediFile.parse();
        ediFile.transform();
        ediFile.summarize();

        log.debug({
          title: "EDI File Parser Email Object",
          details: JSON.stringify(ediFile.parser.email),
        });
      } else {
        log.audit({
          title: "EDI TrueCommerce 810 MR (map)",
          details: `File is billed to Valmar Surgical Supplies. Skipped processing ${filename}`,
        });
      }
      try {
        ediFile.archive({
          filename,
          source: truecommerce.prodDirectory,
          target: truecommerce.referenceDirectory,
        });
      } catch (err) {
        throw ediFile.customError.updateError({
          errorType: ediFile.customError.ErrorTypes.UNHANDLED_ERROR,
          summary: "ARCHIVING",
          details: `ERROR ${JSON.stringify(err)} `,
        });
      }
    } else {
      log.error({
        title: `EDI TrueCommerce 810 IN MR (map): ${ediFile.customError.ErrorTypes.MISSING_VALUE}`,
        details: `EMPTY_FILE: Downloaded ${filename} but is empty.`,
      });

      throw ediFile.customError.updateError({
        errorType: ediFile.customError.ErrorTypes.MISSING_VALUE,
        summary: "EMPTY_FILE",
        details: `Downloaded ${filename} but is empty.`,
      });
    }
  }

  /**
   * Log files that were processed and not processed
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    let filesProcessed = 0;
    let filesNotProcessed = 0;

    context.mapSummary.keys
      .iterator()
      .each((key, executionCount, completionState) => {
        if (completionState === "COMPLETE") {
          filesProcessed++;
        } else {
          filesNotProcessed++;
        }
        return true;
      });

    log.audit({
      title: "EDI TrueCommerce 810 MR (summarize)",
      details: `# of Files processed: ${filesProcessed}, # of Files skipped: ${filesNotProcessed}`,
    });
  }

  /**
   * Determine if the file is billed to Valmar Surgical Supplies
   * We will skip such files instead of throwing errors
   *
   * @param {string} fileContent EDI File Content
   * @returns {boolean}
   */
  function isBilledToValmarSurgicalSupplies(fileContent) {
    return fileContent
      .split(/\n\r/)
      .join("")
      .includes("~N1^BT^VALMAR SURGICAL SUPPLIES");
  }

  return {
    getInputData,
    map,
    summarize,
  };
});
