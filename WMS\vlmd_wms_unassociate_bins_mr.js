/**
 * @description Unassociates bins from items that have not been stored in the bin in x amount of days OR if the item was made inactive.
 * 
 * </br><b>Schedule:</b> Daily
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * 
 * <AUTHOR>
 * @module vlmd_wms_unassociate_bins_mr
 */
define([
	"require",
	"N/record",
	"N/query",
	"N/runtime",
	"../Classes/vlmd_custom_error_object",
	"../Classes/vlmd_mr_summary_handling",
],(require) => {
        const record = require("N/record");
        const query = require("N/query");
        const runtime = require("N/runtime");
		
        const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
        const customErrorObject = new CustomErrorObject();
		
	function getInputData(context) {
		// Get items if their preferred bin is empty.
		let sqlQuery = /*sql*/ `
			SELECT
				bin.binnumber,
				bin.id AS bin_id,
				BUILTIN.DF (item_bin_qty.item) AS item_name,
				item_bin_qty.item AS item_id,
				item.isinactive,
				SUM(item_bin_qty.onhand) AS current_on_hand
			FROM
				bin
				JOIN itembinquantity AS item_bin_qty 
					ON bin.id = item_bin_qty.bin AND item_bin_qty.preferredbin = 'T'
				JOIN item 
					ON item.id = item_bin_qty.item
			GROUP BY
				bin.binnumber,
				bin.id,
				BUILTIN.DF (item_bin_qty.item),
				item_bin_qty.item,
				item.isinactive
			HAVING
				SUM(item_bin_qty.onhand) = 0
		`;

		let preferredBinsWithoutStockAndMovement = query.runSuiteQL({
			query: sqlQuery,
		})?.asMappedResults();

		return preferredBinsWithoutStockAndMovement;
	}

	function reduce(context) {
		try {
			log.audit('Item and Bin being processed:', context.values[0]);
			let inputValues = JSON.parse(context.values[0]);

			let numDaysWithoutMovementParam = runtime.getCurrentScript().getParameter({
				name: "custscript_num_days_w_o_movement",
			});

			if (!numDaysWithoutMovementParam) {	
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.MISSING_PARAM,
					summary: "MISSING_PARAMETER",
					details: `Script parameter custscript_num_days_w_o_movement is missing.`,
				});
			}

			// Get the item's last movement date
			let sqlQuery = /*sql*/ `
				SELECT * FROM (SELECT
					BUILTIN.DF(inv_assign.bin) AS bin,    				
					MAX(transaction.createdDate) AS last_movement_date,
					MAX(BUILTIN.DF(inv_assign.transaction)) AS last_transaction_number,
					MAX(transaction.id) AS transaction_id,
				FROM
					inventoryassignment AS inv_assign
				JOIN
					transaction 
					ON transaction.id = inv_assign.transaction
				JOIN
					transactionline AS tran_line 
					ON tran_line.transaction = inv_assign.transaction
				WHERE
					tran_line.item = ?
					AND tran_line.isinventoryaffecting = 'T'
					AND BUILTIN.DF(inv_assign.bin) = ?
				GROUP BY
					BUILTIN.DF(tran_line.item),
					BUILTIN.DF(inv_assign.bin),
						inv_assign.quantity
				ORDER BY
					MAX(transaction.createdDate) DESC)
				WHERE ROWNUM = 1
			`;

			let itemLastMovementDetails = query.runSuiteQL({
				query: sqlQuery,
				params: [inputValues.item_id, inputValues.binnumber],
			})?.asMappedResults()[0];

			// if (!itemLastMovementDetails) {
			// 	log.audit('Skipping Item and Bin combo', `No movement found for item ${inputValues.item_id} and bin ${inputValues.binnumber}`);
			// 	return;
			// }
			
			// let lastMovementDate = new Date(itemLastMovementDetails.last_movement_date);
			let lastMovementDate = new Date();
			let cutoffDate = new Date();

			cutoffDate.setDate(new Date().getDate() - numDaysWithoutMovementParam);

			if(lastMovementDate < cutoffDate || inputValues.item_is_inactive == 'T' || true){
				// Unassociate bin from Item Record
				let itemRecord = record.load({
					type: record.Type.INVENTORY_ITEM,
					id: inputValues.item_id,
				});

				let lineNumber = itemRecord.findSublistLineWithValue({
					sublistId: 'binnumber',
					fieldId: 'binnumber',
					value: inputValues.bin_id
				});

				itemRecord.removeLine({
					sublistId: 'binnumber',
					line: lineNumber,
				});	
				
				// itemRecord.setValue({
				// 	fieldId: 'custitem_vlmd_last_movement_date',
				// 	value: lastMovementDate
				// });

				// itemRecord.setValue({
				// 	fieldId: 'custitem_vlmd_lst_mvmnt_trnsctn',
				// 	value: itemLastMovementDetails.transaction_id
				// });

				itemRecord.save({
					enableSourcing: false,
					ignoreMandatoryFields: true
				});

				// Create an Bin-Item event log
				let binItemEventLog = record.create({
					type: "customrecord_spl_bin_item_event_log"
				});

				binItemEventLog.setValue({
					fieldId: "custrecord_spl_biel_item",
					value: inputValues.item_id,
				});

				binItemEventLog.setValue({
					fieldId: "custrecord_spl_biel_bin",
					value: inputValues.bin_id,
				});

				binItemEventLog.setValue({
					fieldId: "custrecord_spl_biel_event_timestamp",
					value: new Date(),
				});

				binItemEventLog.setValue({
					fieldId: "custrecord_spl_biel_event_type",
					value: 'Unassociated',
				});

				binItemEventLog.save({
					enableSourcing: false,
					ignoreMandatoryFields: true
				});

				context.write({
					key: "Processed",
					value: inputValues,
				});
			}
		} catch (err) {
			customErrorObject.throwError({
				summaryText: `REDUCE_ERROR`,
				error: err,
				errorWillBeGrouped: true,
			});
		}
	}

	function summarize(context) {
		const StageHandling = require("../Classes/vlmd_mr_summary_handling");
		const stageHandling = new StageHandling(context);
  
		stageHandling.printScriptProcessingSummary();
  
		stageHandling.printRecordsProcessed({
			includeLineBreak: true,
			includeKey: true
		});
  
		stageHandling.printErrors({
			groupErrors: true,
		});
	}

	return {
		getInputData,
		reduce,
		summarize,
	};
});
