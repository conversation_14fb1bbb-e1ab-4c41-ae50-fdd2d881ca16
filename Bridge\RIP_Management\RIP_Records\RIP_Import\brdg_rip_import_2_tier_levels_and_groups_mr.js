/**
 * @description RIP Import 2:
 * The RIP Import records contain 5 sets of Unit of Measure, Quantity and $ Amount
 * Each set represents a Tier Level, and these 5 Tier Levels represent a Tier Group
 *
 * 1. Search if there's already a Tier Level with the uom, quantity and amount combination
 * 2. Create the Tier Level in map() based from the uom, quantity and amount if none exists
 * 3. Write the tier data to reduce context identified by the RIP Import record ID
 * 4. Create the Tier Group in reduce() and use the Tier Levels assigned to the group
 *
 * Submit the import agreements MR once the tier levels and groups are created
 *
 * </br><b>Schedule:</b> On-demand, called by MR
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_import_tier_levels_and_groups_mrg
 */

define([
	"require",
	"BridgeHelperFunctionsLib",
	"N/log",
	"N/record",
	"N/query",
	"N/email",
	"N/runtime",
	"N/task",
	"../../../../Helper_Libraries/vlmd_record_module_helper_lib",
	"../../../../Classes/vlmd_custom_error_object",
	"../../../../Classes/vlmd_mr_summary_handling",
], (
	/** @type {any} */ require,
	/** @type {import("../../Libraries/brdg_helper_functions_lib")} */ bridgeHelperFunctionsLib
) => {
	const log = require("N/log");
	const record = require("N/record");
	const query = require("N/query");
	const email = require("N/email");
	const runtime = require("N/runtime");
	const task = require("N/task");

	/** @type {import("../../../../Helper_Libraries/vlmd_record_module_helper_lib")}*/
	const recordHelperLib = require("../../../../Helper_Libraries/vlmd_record_module_helper_lib");

	/** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
	const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");
	const customErrorObject = new CustomErrorObject();

	const notificationAuthor = 223244; //<EMAIL>
	const maxNumberOfTiersPerGroup = 5; // Each Tier Group can have a maximum of 5 Tier Levels
	const maxErrorThreshold = 10;

	/**
	 * Load Bridge RIP Import records
	 * For every uom, qty and amt grouping,
	 * Include the RIP Import Record ID to be able to group the tier levels in reduce
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
	 * @returns {string[]|undefined}
	 */
	function getInputData(context) {
		const currentScript = runtime.getCurrentScript();
		const runSpecificImportRecord = currentScript.getParameter({
			name: "custscript_specific_rip_import_id",
		});

		try {
			// There are only 5 tiers, and the last is marked by the Tier 5 UOM column
			const finalTierUomIndex = 13;
			let importedRipRecordsArr = [];

			//Get all inactive RIP import records
			const baseQuery = `SELECT
            id,
            custrecord_brdg_rip_import_uom_1,
            custrecord_brdg_rip_import_qty_1,
            custrecord_brdg_rip_import_amt_1,
            custrecord_brdg_rip_import_uom_2,
            custrecord_brdg_rip_import_qty_2,
            custrecord_brdg_rip_import_amt_2,
            custrecord_brdg_rip_import_uom_3,
            custrecord_brdg_rip_import_qty_3,
            custrecord_brdg_rip_import_amt_3,
            custrecord_brdg_rip_import_uom_4,
            custrecord_brdg_rip_import_qty_4,
            custrecord_brdg_rip_import_amt_4,
            custrecord_brdg_rip_import_uom_5,
            custrecord_brdg_rip_import_qty_5,
            custrecord_brdg_rip_import_amt_5
          FROM customrecord_brdg_rip_import
          WHERE isinactive = 'F'
          `;
			const whereItemExistsInNs = ` and custrecord_item_exists_in_ns = 'T'`;
			const checkForNewItemWhereClause = `AND custrecord_item_exists_in_ns = 'F' AND id = ${runSpecificImportRecord}`;

			query
				.runSuiteQLPaged({
					query: runSpecificImportRecord
						? `${baseQuery}${checkForNewItemWhereClause}`
						: `${baseQuery}${whereItemExistsInNs}`,
					pageSize: 1000,
				})
				.iterator()
				.each((page) => {
					/*Group results into tier levels - every 3 values get grouped into a tier level (UOM, quantity, and amount).        
          Creates a max of 5 tier levels per import record
            Sample End Result: 
              [
              {
                "importRecordId": 2602,
                "tier": 1,
                "uom": 2,
                "qty": 3,
                "amt": 10
              },
              {
                "importRecordId": 2602,
                "tier": 2,
                "uom": 2,
                "qty": 6,
                "amt": 100
              },
              ]*/
					page.value.data.results.forEach((result) => {
						const importRecordId = result.values[0];
						for (
							let newTierLevelIndex = 1, tierLevelsCounter = 1;
							newTierLevelIndex <= finalTierUomIndex
								&& tierLevelsCounter <= maxNumberOfTiersPerGroup;
							newTierLevelIndex += 3, tierLevelsCounter++
						) {
							if (
								result.values[newTierLevelIndex] &&
								result.values[newTierLevelIndex + 1] &&
								result.values[newTierLevelIndex + 2]
							) {
								importedRipRecordsArr.push({
									importRecordId,
									tier: tierLevelsCounter,
									uom: /^(Bottles|Bottle\(s\))$/.test(result.values[newTierLevelIndex]?.toString() || "") ? 1 : 2,
									qty: result.values[newTierLevelIndex + 1],
									amt: result.values[newTierLevelIndex + 2],
								});
							}
						}
					});

					return true;
				});
			return importedRipRecordsArr;
		} catch (err) {
			customErrorObject.throwError({
				summaryText: "GET_INPUT_DATA_ERROR",
				error: err,
			});
		}
	}

	/**
	 * Create Tier Levels
	 * Based on the uom, qty and amt, search for an existing tier
	 *   and create a new Tier Level if there's no result
	 * Group the tier levels, combining old and new records,
	 *   that appear on the same RIP Import record
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
	 * @returns {void}
	 */
	function map(context) {
		const mapErrorObject = new CustomErrorObject();
		const { importRecordId, tier, uom, qty, amt } = JSON.parse(context.value);

		try {
			//Check if a tier level record exists for this combination of UOM, quantity, and amount.
			const tierQuery = query.runSuiteQL({
				query: `
          SELECT id, name
          FROM customrecord_rebate_tier_level
          WHERE custrecord_unit_of_measure = ${uom}
          AND custrecord_tier_quantity = ${qty}
          AND custrecord_dollar_off = ${amt}
          AND isinactive = 'F'
        `,
			});

			let tierLevelRecordId;
			let tierLevelName;

			if (tierQuery.results.length > 0) {
				//Tier level already exists - set the values for the id and name of existing record
				tierLevelRecordId = tierQuery.results[0].values[0];
				tierLevelName = tierQuery.results[0].values[1];
			} else {
				//No tier level, create a new one and set the values for the id and name of existing record
				const mappedValuesObj = {
					name: `${qty} $${amt} Off ${uom === 1 ? "Bottles" : "Case(s)"}`, // E.g. 2 $20 Off Case(s)
					custrecord_unit_of_measure: uom,
					custrecord_tier_quantity: qty,
					custrecord_dollar_off: amt,
				};
				tierLevelName = mappedValuesObj.name;

				const tierLevel = record.create({
					type: "customrecord_rebate_tier_level",
					isDynamic: true,
				});

				try {
					recordHelperLib.setBodyValues(mappedValuesObj, tierLevel);
				} catch (err) {
					throw mapErrorObject.updateError({
						errorType: mapErrorObject.ErrorTypes.VALUE_NOT_SET,
						summary: "ERROR_SETTING_TIER_LEVEL_VALUES",
						details: err.message,
					});
				}

				try {
					tierLevelRecordId = tierLevel.save();
				} catch (err) {
					throw mapErrorObject.updateError({
						errorType: mapErrorObject.ErrorTypes.RECORD_NOT_SAVED,
						summary: "ERROR_SAVING_TIER_LEVEL_RECORD",
						details: err.message,
					});
				}
			}

			//Will group tier levels by import record
			context.write({
				key: importRecordId,
				value: {
					tier,
					tierLevelRecordId,
					tierLevelName,
				},
			});
		} catch (err) {
			mapErrorObject.throwError({
				summaryText: `MAP_ERROR`,
				error: err,
				recordId: importRecordId,
				recordType: "customrecord_rebate_tier_level",
				errorWillBeGrouped: true,
			});
		}
	}

	/**
	 * Iterate over the context values for each import record
	 * Create Tier groups
	 * The context will contain tier levels that should be grouped together
	 * Search for a tier group that is comprised by the tier levels,
	 *   and create a new tier group if there is no result
	 * On error, throw the error including the context.values
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
	 * @returns {void}
	 */
	function reduce(context) {
		const reduceErrorObject = new CustomErrorObject();

		try {
			const tierFields = {
				1: "custrecord_tier_level_1",
				2: "custrecord_tier_level_2",
				3: "custrecord_tier_level_3",
				4: "custrecord_tier_level_4",
				5: "custrecord_tier_level_5",
			};

			/*Sample context values
          [
            "{\"tier\":1,\"tierLevelRecordId\":256,\"tierLevelName\":\"3 $10 Off Case(s)\"}",
            "{\"tier\":2,\"tierLevelRecordId\":487,\"tierLevelName\":\"6 $100 Off Case(s)\"}",
            "{\"tier\":3,\"tierLevelRecordId\":271,\"tierLevelName\":\"12 $240 Off Case(s)\"}"
          ] */

			const tierLevelIds = {};
			const tierNames = {};

			/*Iterate over tier levels and build up an object of tier level ids and an object of tier level names to be used in the query
          Sample Tier Level Ids: 
            {
              "1": 256,
              "2": 487,
              "3": 271
            }

          Sample Tier Level Names: 
            {
              "1": "3 $10 Off Case(s)",
              "2": "6 $100 Off Case(s)",
              "3": "12 $240 Off Case(s)"
            }*/
			for (let i in context.values) {
				const { tier, tierLevelRecordId, tierLevelName } = JSON.parse(
					context.values[i]
				);
				tierLevelIds[tier] = tierLevelRecordId;
				tierNames[tier] = tierLevelName;
			}

			/*Check if an active tier group exists for this combination of tier levels
          Sample Query:
            SELECT id, name
            FROM customrecord_rebate_tier_group
            WHERE custrecord_tier_level_1 = 256 
              AND custrecord_tier_level_2 = 487 
              AND custrecord_tier_level_3 = 271 
              AND custrecord_tier_level_4 IS NULL 
              AND custrecord_tier_level_5 IS NULL
              AND isinactive = 'F' */

			const tierQuery = query.runSuiteQL({
				query: `
          SELECT id, name
          FROM customrecord_rebate_tier_group
          WHERE ${bridgeHelperFunctionsLib.getTierFilters(
						tierLevelIds,
						tierFields
					)}
          AND isinactive = 'F'
        `,
			});

			if (tierQuery.results.length < 1) {
				//No tier group for this combination of tier levels -> create a new tier group
				const tierGroupRecord = record.create({
					type: "customrecord_rebate_tier_group",
					isDynamic: true,
				});

				const tierGroupName =
					bridgeHelperFunctionsLib.getTierGroupName(tierNames);

				if (!tierGroupName) {
					throw customErrorObject.updateError({
						errorType: customErrorObject.ErrorTypes.VALUE_NOT_SET,
						summary: "NO_TIER_GROUP_NAME_RETURNED",
						details: `No tier group name returned for tier names, ${tierNames}`,
					});
				}

				try {
					tierGroupRecord.setValue({
						fieldId: "name",
						value: tierGroupName,
					});
				} catch (err) {
					throw reduceErrorObject.updateError({
						errorType: reduceErrorObject.ErrorTypes.VALUE_NOT_SET,
						summary: "ERROR_SETTING_NAME_VALUE",
						details: err.message,
					});
				}

				//Set all values for all tier level fields
				for (let i = 1; i <= maxNumberOfTiersPerGroup; i++) {
					try {
						tierGroupRecord.setValue({
							fieldId: tierFields[i],
							value: tierLevelIds[i],
						});
					} catch (err) {
						throw reduceErrorObject.updateError({
							errorType: reduceErrorObject.ErrorTypes.VALUE_NOT_SET,
							summary: "ERROR_SETTING_VALUES",
							details: `Tier field: ${tierFields[i]}, Tier level: ${tierLevelIds[i]}, ${err.message}`,
						});
					}
				}

				try {
					const tierGroupId = tierGroupRecord.save();
					context.write("Tier Group Created", tierGroupId);
				} catch (err) {
					throw reduceErrorObject.updateError({
						errorType: reduceErrorObject.ErrorTypes.RECORD_NOT_SAVED,
						summary: "ERROR_SAVING_IMPORT_RECORD",
						details: err.message,
					});
				}
			}
		} catch (err) {
			let importRecord = record.load({
				type: "customrecord_brdg_rip_import",
				id: context.key,
			});

			let existingError = importRecord.getValue(
				"custrecord_brdg_rip_imp_error_importing"
			);

			importRecord.setValue(
				"custrecord_brdg_rip_imp_error_importing",
				`${existingError}REDUCE_ERROR_TIER_GROUP, ${err.name}, ${err.message}\n`
			);

			importRecord.save();

			reduceErrorObject.throwError({
				summaryText: `REDUCE_ERROR`,
				error: err,
				recordId: context.key,
				recordName: context.values,
				recordType: "customrecord_brdg_rip_import",
				errorWillBeGrouped: true,
			});
		}
	}

	/**
	 * The summarize stage of the Map/Reduce script.
	 * Submit the RIP Import Agreements MR on a successfull execution
	 * On Get Input Data Error,
	 *   throw the error
	 *   do not trigger the Agreements MR
	 *   send an email notification to the process owner
	 * On Map Error,
	 *   collate the tier levels from the RIP Import records into an HTML table
	 *   error count <= 10 - send an email containing the table
	 *   error count > 10 - send an email saying there's too many map stage errors
	 * On Reduce Error,
	 *   collate the tier groups into an HTML table
	 *   error count <= 10 - send an email containing the table
	 *   error count > 10 - send an email saying there's too many reduce stage errors
	 *   trigger Agreements MR if error count <= 10
	 *
	 * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
	 * @returns {void}
	 */
	function summarize(context) {
		try {
			const currentScript = runtime.getCurrentScript();

			var ownerId =
				currentScript.getParameter({
					name: "custscript_brdg_rip_import_tier_owner",
				}) ?? 3288;
			const ripImportRecordId = currentScript.getParameter({
				name: "custscript_specific_rip_import_id",
			});

			// Error in getInputData stage -> no records were processed -> stop execution. Generic error email will be sent to user.
			if (context.inputSummary.error) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
					summary: "ERROR_IN_GET_INPUT_DATA",
					details: `MR 2: Import process stopped because data to process wasn't loaded successfully.`,
				});
			}

			const StageHandling = require("../../../../Classes/vlmd_mr_summary_handling");
			const stageHandling = new StageHandling(context);

			stageHandling.printScriptProcessingSummary();
			let { resultsLog, recordsProcessedMessage } =
				stageHandling.printRecordsProcessed();
			let { errorArr, errorsMessage } = stageHandling.printErrors({
				groupErrors: true,
				formatMessageForEmail: true,
			});

			// Too many errors, likely something wrong with the entire file -> stop execution. Generic error email will be sent to user.
			if (errorArr.length > maxErrorThreshold) {
				throw customErrorObject.updateError({
					errorType: customErrorObject.ErrorTypes.UNHANDLED_ERROR,
					summary: "TOO_MANY_MAP_ERRORS",
					details: "Import process stopped because there were too many errors.",
				});
			}

			if (errorArr.length > 0) {
				email.send({
					author: notificationAuthor,
					recipients: [ownerId],
					subject: "Script #2: Bridge RIP Import Tiers Error",
					body: `An error occurred while importing these rows from the RIP file.
Please address errors below and create tier levels, tier groups, agreement records, agreement details manually\n\n
${errorsMessage}`,
				});
			}

			const messageFromPreviousStage = currentScript.getParameter({
				name: "custscript_brdg_rip_import_rcrds_message",
			});

			let processingMessage = `${messageFromPreviousStage}
<b>Script #2: Create Any Tier Levels and Tier Groups Needed</b><br/>

${resultsLog.length} Tier Group${
				resultsLog.length == 1 ? "" : "s"
			} Created.<br/>
${errorArr.length} Error${errorArr.length == 1 ? "" : "s"}${
				errorsMessage ? ": " + errorsMessage : "."
			}<br/><br/>`;

			const specificItemIdAdded = currentScript.getParameter({
				name: "custscript_item_id_added",
			});

			const createAgreementsMr = task.create({
				taskType: task.TaskType.MAP_REDUCE,
				scriptId: "customscript_brdg_rip_import_agrmnt_mr",
				deploymentId: "customdeploy_brdg_rip_import_agrmnt_mr",
				params: {
					custscript_brdg_rip_import_agrmnt_owner: ownerId || "",
					custscript_brdg_rip_import_tiers_message: processingMessage,
					custscript_specific_rip_id: ripImportRecordId,
					custscript_item_id_added_2: specificItemIdAdded,
				},
			});
			createAgreementsMr.submit();
		} catch (err) {
			email.send({
				author: notificationAuthor,
				recipients: [ownerId],
				subject: "Bridge RIP Import Tiers Error",
				body: `Import process stopped because there was an error while processing.<br><br>Please reach <NAME_EMAIL> for assistance.`,
			});

			customErrorObject.throwError({
				summaryText: `SUMMARIZE_ERROR`,
				error: err,
			});
		}
	}

	return {
		getInputData,
		map,
		reduce,
		summarize,
	};
});
