/**
 * @description RIP Import 4:
 * Create RIP Import Agreement Detail Item records
 * Attach them to a parent Agreement Detail record
 *
 * </br><b>Schedule:</b> On-demand, called by MR
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 * @NAmdConfig /SuiteScripts/config.json
 *
 * <AUTHOR>
 * @module brdg_rip_import_4_agreement_detail_items_mr
 */

define([
  "require",
  "BridgeHelperFunctionsLib",
  "N/log",
  "N/record",
  "N/email",
  "N/runtime",
  "N/query",
  "N/search",
  "N/task",
  "../../../../Classes/vlmd_custom_error_object",
  "../../../../Classes/vlmd_mr_summary_handling",
  "../../../../Helper_Libraries/vlmd_record_module_helper_lib",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const record = require("N/record");
  const email = require("N/email");
  const runtime = require("N/runtime");
  const query = require("N/query");
  const search = require("N/search");
  const task = require("N/task");

  /** @type {import("../../../../Helper_Libraries/vlmd_record_module_helper_lib")} */
  const recordHelperLib = require("../../../../Helper_Libraries/vlmd_record_module_helper_lib");

  /** @type {import("../../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../../Classes/vlmd_custom_error_object");

  const notificationAuthor = 223244; //<EMAIL>
  const maxErrorThreshold = 10;

  /**
   * Look up the items from the active RIP Import Records
   *  that are associated with the details passed to the MR
   *
   * @param {import("N/types").EntryPoints.MapReduce.getInputDataContext} context Get input data context
   * @returns {string[]|undefined}
   */
  function getInputData(context) {
    const getInputErrorObject = new CustomErrorObject();
    try {
      const currentScript = runtime.getCurrentScript();
      const detailIdsString = currentScript.getParameter({
        name: "custscript_brdg_rip_import_item_details",
      })?.toString();

      return detailIdsString ? detailIdsString.split(",") : [];
    } catch (/** @type {any} */ err) {
      getInputErrorObject.throwError({
        summaryText: "GET_INPUT_DATA_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  /**
   * Retrieve all items associated with the Agreement Detail record
   *
   * @example {"123": {"countAs":1}}
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Map context
   * @returns {void}
   */
  function map(context) {
    const mapErrorObject = new CustomErrorObject();
    const detailId = context.value;

    try {
      const includedItems = search.lookupFields({
        type: "customrecord_rebate_agreement_detail",
        id: detailId,
        columns: ["custrecord_rebate_items_included"]
      })["custrecord_rebate_items_included"];

      if (Array.isArray(includedItems)) {
        includedItems.map(item => {
          context.write(item.value, detailId);
        });
      }
    } catch (err) {
      mapErrorObject.throwError({
        summaryText: "MAP_ERROR",
        error: err,
        recordId: detailId,
        recordName: null,
        recordType: "customrecord_rebate_agreement_detail",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * Create a RIP Agreement Detail Item record
   *
   * @param {import("N/types").EntryPoints.MapReduce.reduceContext} context Reduce context
   * @returns {void}
   */
  function reduce(context) {
    const reduceErrorObject = new CustomErrorObject();
    const itemId = context.key;

    try {
      const detailId = context.values[0];
      let countAs = 0;

      query.runSuiteQLPaged({
        query: `
          SELECT
            BUILTIN_RESULT.TYPE_FLOAT(custrecord_brdg_rip_import_count_as)
          FROM
            customrecord_brdg_rip_import
          WHERE
            custrecord_brdg_rip_import_item = ${itemId}
            AND isinactive = 'F'
          ORDER BY id DESC
        `,
        pageSize: 1000,
      }).iterator().each((page) => {
        page.value.data.results.forEach((result) => {
          countAs = Number(result.values[0]);
        });
        return true;
      });

      // Return if no active import record exists
      // Count As should never be 0
      if (!countAs) {
        return;
      }

      const duplicateValidationQuery = `
        SELECT
          id
        FROM
          customrecord_rebate_agreement_detail_itm
        WHERE
          custrecord_agreement_detail = '${detailId}'
          AND custrecord_agreement_detail_item_count = ${countAs}
          AND custrecord_agreement_detail_item = '${itemId}'
          AND isinactive = 'F'
      `;

      const validateNoDuplicateQueryResults = query.runSuiteQL({
        query: duplicateValidationQuery,
      });

      let detailItemRecordId;
      if (validateNoDuplicateQueryResults.results.length <= 0) {
        const detailItemRecord = record.create({
          type: "customrecord_rebate_agreement_detail_itm",
          isDynamic: true,
        });
  
        const mappedValuesObj = {
          custrecord_agreement_detail: detailId,
          custrecord_agreement_detail_item_count: countAs,
          custrecord_agreement_detail_item: itemId,
        };
  
        try {
          // @ts-ignore Property 'setBodyValues' does not exist on type 'typeof import("vlmd_record_module_helper_lib")'.ts(2339)
          recordHelperLib.setBodyValues(mappedValuesObj, detailItemRecord);
        } catch (/** @type {any} */ err) {
          throw reduceErrorObject.updateError({
            errorType: reduceErrorObject.ErrorTypes.VALUE_NOT_SET,
            summary: "ERROR_SETTING_AGREEMENT_RECORD_VALUES",
            details: err.message,
          });
        }
  
        detailItemRecordId = detailItemRecord.save();
      } else {
        detailItemRecordId = validateNoDuplicateQueryResults.results[0].values[0];
        log.debug("Existing Agreement Detail Item Record", detailItemRecordId)
      }

      if (!detailItemRecordId) {
        throw reduceErrorObject.updateError({
          errorType: reduceErrorObject.ErrorTypes.RECORD_NOT_SAVED,
          summary: "ERROR_SAVING_DETAIL_ITEM_RECORD",
          details: `Rebate agreement detail item record not saved successfully.`,
        });
      }

      context.write("Rebate Agreement Detail Item ID", detailItemRecordId.toString());
    } catch (err) {
      log.error("Reduce Error", err);
      reduceErrorObject.throwError({
        summaryText: "REDUCE_ERROR",
        error: err,
        recordId: itemId,
        recordName: null,
        recordType: "customrecord_rebate_agreement_detail_itm",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * The summarize stage of the Map/Reduce script.
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    const summarizeErrorObject = new CustomErrorObject();
    const currentScript = runtime.getCurrentScript();
    const ownerId =
      Number(currentScript.getParameter({
        name: "custscript_brdg_rip_import_item_owner",
      }) || 0) ?? 3288;

    try {
      // Error in getInputData stage -> no records were processed -> stop execution.
      if (context.inputSummary.error) {
        throw summarizeErrorObject.updateError({
          errorType: summarizeErrorObject.ErrorTypes.NO_VALUE_RETURNED,
          summary: "ERROR_IN_GET_INPUT_DATA",
          details:
            "MR 4: Import process stopped because data to process wasn't loaded successfully.",
        });
      }

      const StageHandling = require("../../../../Classes/vlmd_mr_summary_handling");
      // @ts-ignore Type 'typeof import("vlmd_mr_summary_handling")' has no construct signatures.ts(2351)
      const stageHandling = new StageHandling(context);

      stageHandling.printScriptProcessingSummary();
      const { resultsLog, recordsProcessedMessage } = stageHandling.printRecordsProcessed();
      const { errorArr, errorsMessage } = stageHandling.printErrors({
        groupErrors: true,
        formatMessageForEmail: true,
      });

      // Too many errors, likely something wrong with the entire file -> stop execution.
      if (errorArr.length > maxErrorThreshold) {
        throw summarizeErrorObject.updateError({
          errorType: summarizeErrorObject.ErrorTypes.UNHANDLED_ERROR,
          summary: "TOO_MANY_MAP_ERRORS",
          details: "Import process stopped because there were too many errors.",
        });
      }

      if (errorArr.length > 0) {
        email.send({
          author: notificationAuthor,
          recipients: [ownerId],
          subject: "Bridge RIP Agreement Detail Items Error",
          body: `An error occurred while creating RIP Import Agreement Detail Item records. 
Please address errors below and create agreement detail items manually\n\n
${errorsMessage}`,
        });
      }

      const messageFromPreviousStage = currentScript.getParameter({
        name: "custscript_brdg_rip_agreement_message",
      });

      const processingMessage = `${messageFromPreviousStage}
<b>Script #4: Create RIP Agreement Detail Items</b><br/>
${resultsLog.length} Rebate Agreement Detail Item${
        resultsLog.length == 1 ? "" : "s"
      } Created (internal ids):<br/>
${recordsProcessedMessage ? recordsProcessedMessage + "<br/><br/>" : ""}
${errorArr.length} Error${errorArr.length == 1 ? "" : "s"}${
        errorsMessage ? ": " + errorsMessage : "."
      }<br/><br/>`;

      const importCleanupMr = task.create({
        taskType: task.TaskType.MAP_REDUCE,
        scriptId: "customscript_brdg_rip_import_cleanup_mr",
        deploymentId: "customdeploy_brdg_rip_import_cleanup_mr",
        params: {
          custscript_brdg_rip_import_cleanup_owner: ownerId || "",
          custscript_brdg_rip_detail_items_message: processingMessage,
        },
      });

      importCleanupMr.submit();
    } catch (err) {
      email.send({
        author: notificationAuthor,
        recipients: [ownerId],
        subject: "Bridge RIP Import Agreements Error",
        body: `Import process stopped because there was an error while processing.<br><br>Please reach <NAME_EMAIL> for assistance.`,
      });

      summarizeErrorObject.throwError({
        summaryText: "SUMMARIZE_ERROR",
        error: err,
        recordId: null,
        recordName: null,
        recordType: null,
        errorWillBeGrouped: false,
      });
    }
  }

  return {
    getInputData,
    map,
    reduce,
    summarize,
  };
});
