/**
 * @description Create delta records and a new price file for each parent customer with price changes.
 *
 * </br><b>Schedule:</b> Runs every night at 12:30 AM
 *
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 *
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 */

define([
  "require",
  "N/log",
  "N/runtime",
  "./Classes/price_changes_customer_level",
  "../../Classes/vlmd_custom_error_object",
  "../../Classes/vlmd_mr_summary_handling",
], (/** @type {any} */ require) => {
  const log = require("N/log");
  const runtime = require("N/runtime");

  /** @type {import("./Classes/price_changes_customer_level")} */
  const PriceChangesFactory =
    require("./Classes/price_changes_customer_level").CustomerPriceChangesFactory;

  /** @type {import("../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../Classes/vlmd_custom_error_object");

  /**
   * Get all parents who have a price change within the given time period.
   *
   * @returns {SuiteQLObjectReference|undefined} Object containing SuiteQL string
   */
  function getInputData() {
    const customError = new CustomErrorObject();
    try {
      const currentScript = runtime.getCurrentScript();

      const numberOfDaysBack = currentScript.getParameter({
        name: "custscript_number_of_days_back",
      });

      const customerId = currentScript.getParameter({
        name: "custscript_customer_id",
      });

      log.audit(
        "Get Input Data Params",
        `Number of Days Back: ${JSON.stringify({
          numberOfDaysBack,
        })}, Customer ID: ${JSON.stringify({
          customerId,
        })}`
      );

      let priceChangesFactory = new PriceChangesFactory();

      priceChangesFactory.getParentsWithPriceChangesQueryString(
        numberOfDaysBack,
        customerId
      );

      log.audit(
        "Query String | Parent Customers With Price Changes",
        priceChangesFactory.queryString
      );

      return {
        type: "suiteql",
        query: priceChangesFactory.queryString,
        params: [],
      };
    } catch (err) {
      customError.throwError({
        summaryText: "GET_INPUT_DATA",
        error: err,
      });
    }
  }

  /**
   * Create a new price file for the customer specifying which items are the deltas and call script to create delta records
   *
   * @param {import("N/types").EntryPoints.MapReduce.mapContext} context Reduce context
   * @returns {void}
   */
  function reduce(context) {
    let priceChangesFactory = new PriceChangesFactory();

    try {
      const parsedItemRow = JSON.parse(context.values).values;

      priceChangesFactory.setParentValuesForIteration(parsedItemRow);

      if (
        priceChangesFactory.parentCustomerObj.existingPriceListFileId != null
      ) {
        priceChangesFactory.createMapObjOfOldPrices();
      }

      priceChangesFactory.getNewPriceList();
      priceChangesFactory.generatePriceListFile();
      priceChangesFactory.createCustomerPricingFilesRecord();
      priceChangesFactory.callScriptToCreateSubRecords();

      context.write(
        priceChangesFactory.parentCustomerObj.entityId,
        context.value
      );
    } catch (err) {
      priceChangesFactory.customErrorObject.throwError({
        summaryText: "REDUCE_ERROR",
        error: err,
        recordType: "PRICE",
        errorWillBeGrouped: true,
      });
    }
  }

  /**
   * Log summary results and call the script to handle item level price changes
   *
   * @param {import("N/types").EntryPoints.MapReduce.summarizeContext} context Summarize context
   * @returns {void}
   */
  function summarize(context) {
    /**@typedef {import("../../Classes/vlmd_mr_summary_handling")}*/
    const StageHandling = require("../../Classes/vlmd_mr_summary_handling");
    const stageHandling = new StageHandling(context);

    stageHandling.printErrors({
      groupErrors: true,
    });

    stageHandling.printScriptProcessingSummary();

    const currentScript = runtime.getCurrentScript();
    const numberOfDaysBack = currentScript.getParameter({
      name: "custscript_number_of_days_back",
    });

    const callItemLevelScript = Boolean(
      currentScript.getParameter({
        name: "custscript_call_item_level_script",
      })
    );

    if (callItemLevelScript) {
      let priceChangesFactory = new PriceChangesFactory();
      priceChangesFactory.callScriptToHandleItemLevelPriceChanges(
        numberOfDaysBack
      );
    }
  }

  return {
    getInputData,
    reduce,
    summarize,
  };
});
