/**
 * Contains functions that validate transaction item sublist
 * for Validate Transaction CS' validateLine and saveRecord
 *
 * @NApiVersion 2.1
 * @module spl_get_transaction_and_item_objs_lib
 */
//@ts-ignore
define([
	"CustomItemSublistRecord",
	"ItemFulfillmentItemSublistRecord",
	"SalesOrderItemSublistRecord",
	"CustomTransactionRecord",
	"ItemFulfillmentTransactionRecord",
	"SalesOrderTransactionRecord",
], function (
	CustomItemSublistRecord,
	ItemFulfillmentItemSublistRecord,
	SalesOrderItemSublistRecord,
	CustomTransactionRecord,
	ItemFulfillmentTransactionRecord,
	SalesOrderTransactionRecord
) {
	/**
	 * Retrieve customer and its parent's data
	 *
	 * @param {import("@hitc/netsuite-types/N/record").Record} transaction NetSuite transaction containing line-level data
	 * @returns {{customerId: string, customerParentObj: object}} Object containing the customer ID and details about its parent
	 */
	function getTransactionObjForValidateLine(transaction) {
		/** @type {import("../../Classes/spl_transaction_record").CustomTransactionRecord} */
		const transactionRecord = new CustomTransactionRecord(transaction);

		return {
			customerId: transactionRecord.customerId,
			customerParentObj: transactionRecord.customerParentObj,
			parentIsFreeFreightEligible:
				transactionRecord.parentIsFreeFreightEligible,
			freeFreightItems: transactionRecord.freeFreightItems,
		};
	}

	/**
	 * Retrieve item sublist record as an object
	 *
	 * @param {import("@hitc/netsuite-types/N/record").Record} transaction NetSuite record containing line-level data
	 * @param {boolean} transactionIsSalesOrder True if transaction is a Sales Order
	 * @returns {import("../../Classes/spl_item_sublist_record").SalesOrderItemSublistRecord|import("../../Classes/spl_item_sublist_record").CustomItemSublistRecord} Object representing an item sublist line
	 */
	function getItemObjForValidateLine(transaction, transactionIsSalesOrder) {
		return transactionIsSalesOrder
			? new SalesOrderItemSublistRecord(transaction)
			: new CustomItemSublistRecord(transaction);
	}

	/**
	 * Retrieve transaction record header-level data as an object
	 *
	 * @param {import("@hitc/netsuite-types/N/record").Record} transaction NetSuite transaction record
	 * @param {boolean} transactionIsSalesOrder True if transaction is a Sales Order
	 * @returns {import("../../Classes/spl_transaction_record").SalesOrderTransactionRecord|import("../../Classes/spl_transaction_record").CustomTransactionRecord} Object representing an transaction header-level data
	 */
	function getTransactionObjForSave(transaction, transactionIsSalesOrder) {
		return transactionIsSalesOrder
			? new SalesOrderTransactionRecord(transaction)
			: new CustomTransactionRecord(transaction);
	}

	/**
	 * Retrieve customer and its parent's data
	 *
	 * @param {import("@hitc/netsuite-types/N/record").Record} transaction NetSuite Item Fulfillment record
	 * @returns {{customerName: string, customerIsShipByTruck: boolean}} Object containing the customer data
	 */
	function getIFTransactionObjForSave(transaction) {
		/** @type {import("../../Classes/spl_transaction_record").ItemFulfillmentTransactionRecord} */
		const itemFulfillmentTransaction = new ItemFulfillmentTransactionRecord(
			transaction
		);

		return {
			customerName: itemFulfillmentTransaction.customerName,
			customerIsShipByTruck: itemFulfillmentTransaction.customerIsShipByTruck,
		};
	}

	/**
	 * Retrieve item sublist record as an object
	 *
	 * @param {import("@hitc/netsuite-types/N/record").Record} transaction NetSuite record containing line-level data
	 * @param {boolean} transactionIsSalesOrder True if transaction is a Sales Order
	 * @param {number} x Sublist line index
	 * @returns {import("../../Classes/spl_item_sublist_record").SalesOrderItemSublistRecord|import("../../Classes/spl_item_sublist_record").CustomItemSublistRecord} Object representing an item sublist line
	 */
	function getItemObjForSave(transaction, transactionIsSalesOrder, x) {
		return transactionIsSalesOrder
			? new SalesOrderItemSublistRecord(transaction, x)
			: new CustomItemSublistRecord(transaction, x);
	}

	/**
	 * Retrieve Item Fulfillment data as an object
	 *
	 * @param {import("@hitc/netsuite-types/N/record").Record} transaction NetSuite Item Fulfillment record
	 * @param {number} x Sublist line index
	 * @returns {{itemName: string, itemIsShipByTruck: boolean}} Object containing item-level data
	 */
	function getIFItemObjForSave(transaction, x) {
		/** @type {import("../../Classes/spl_item_sublist_record").ItemFulfillmentItemSublistRecord} */
		const itemFulfillmentItemSublistRecord =
			new ItemFulfillmentItemSublistRecord(transaction, x);

		return {
			itemName: itemFulfillmentItemSublistRecord.itemName,
			itemIsShipByTruck: itemFulfillmentItemSublistRecord.itemIsShipByTruck,
		};
	}

	/**
	 * Run validations for item sublist record
	 *
	 * @param {import("../../Classes/spl_item_sublist_record").CustomItemSublistRecord} item Item object
	 * @returns {{messages: string[], hasError: boolean}} Errors and warning messages
	 */
	function runItemValidations(item) {
		const errorsArr = [];
		const warningsArr = [];

		item.isUsingBasePrice() &&
			errorsArr.push(
				`${item.itemName} - You cannot add an item with the Vendor Cost/Base Price. Please change the price level.`
			);
		item.isFinalSaleItem &&
			warningsArr.push(
				`Item ${item.itemName} is final sale. Please alert the customer.`
			);
		item.rate < item.getMinimumMarkupAmount() &&
			warningsArr.push(
				`The rate for ${item.itemName}, which is ${item.rate},` +
					` is lower than the minimum markup rate of $${item
						.getMinimumMarkupAmount()
						.toFixed(2)} (${item.getMinimumMarkupPercentage()} markup).`
			);

		return {
			messages: errorsArr.concat(warningsArr),
			hasError: errorsArr.length > 0,
		};
	}

	/**
	 * Run validations on the sales order item object
	 *
	 * @param {import("../../Classes/spl_item_sublist_record").SalesOrderItemSublistRecord} item Item object
	 * @returns {{messages: string[], hasError: boolean}} Errors and warning messages
	 */
	function runSalesOrderItemValidations(item) {
		const errorsArr = [];
		const warningsArr = [];

		item.isDropShipItemNotUsingPurchaseOrderRate() &&
			errorsArr.push(
				`Please change the cost estimate type of ${item.itemName}  to "Purchase Order Rate".`
			);
		item.isItemNotUsingAverageCost() &&
			errorsArr.push(
				`Please change the cost estimate type of ${item.itemName}  to "Average Cost".`
			);
		item.isNotDropShipAndNoLocation() &&
			errorsArr.push(
				`${item.itemName} is not drop ship and is missing a location.`
			);
		item.isDropShipItemUsingWrongLocation() &&
			errorsArr.push(
				`${item.itemName} is a drop ship item and has a location other than drop ship chosen.`
			);
		item.shouldDropShipOverBedTable() &&
			warningsArr.push(
				`Being that the quantity of ${item.itemName} is over 10, please drop ship.`
			);
		item.shouldDropShipLowAirLossMattress() &&
			warningsArr.push(
				`Being that the quantity of ${item.itemName} is over 10, please confirm if this should be drop shipped.`
			);
		item.shouldDropShipAndSetVendorCostPMOVBT() &&
			warningsArr.push(
				`Being that the quantity of ${item.itemName} is over 10, please change the vendor cost to be $35 and drop ship the order from Proactive.`
			);
		item.isItemPtr1369() &&
      warningsArr.push(
        `For orders of 1-2 of item PTR1369 please send from Union. For 3+ dropship from Drive.`
      );
    item.isItemPtr1125() &&
      warningsArr.push(
        `For orders of 1-2 overbed tables - PTR1125 - please send from Union. For 3+, dropship from Drive`
      );
    item.needsImmediateEtaUpdate &&
      warningsArr.push(
        `${item.itemName} is a specialty item. After processing order, the customer needs to get an immediate ETA update.`
      );
		item.isLiftGateAlertRequired() &&
			warningsArr.push(
				`${item.itemName} - Please be sure to include the following on the memo to the vendor` +
					` when beds, furniture or large therapy equipment are on the order: (1) Contact name (2) Contact phone number (3) If the liftgate is required`
			);
		item.shouldChangeShippingToMedlineFreight() &&
			warningsArr.push(
				`${item.itemName} - Please change the shipping account to the Medline Freight account.`
			);
		item.shouldChangeShippingToMedlineFlatRate() &&
			warningsArr.push(
				`${item.itemName} - Please change the shipping account to the Medline Flat Rate account.`
			);
		item.shouldChangeMattressVendorToDrive() &&
			warningsArr.push(
				"Please use 15019 and order from Drive instead because the mattress quantity is 4 or greater."
			);
		item.shouldDisplayOrderOnlineProcessingAlert() &&
			warningsArr.push(
				`${item.itemName}: For this vendor, order needs to be processed online as well in order to ship.`
			);
		item.hasQuantityOfFourForRSP1270() &&
			warningsArr.push(
				"For orders of 1-3 of item RSP1270, please send from Union. For 4+, dropship from Rhythm."
			);

		return {
			messages: errorsArr.concat(warningsArr),
			hasError: errorsArr.length > 0,
		};
	}

	/**
	 * Run validations to check item quantity increments
	 *
	 * @param {import("../../Classes/spl_item_sublist_record").CustomItemSublistRecord} item Item object
	 * @returns {string[]} Error messages
	 */
	function runItemIncrementValidations(item) {
		const errorsArr = [];

		item.hasCorrectIncrementsFor400879() &&
			errorsArr.push(
				"Please change the quantity of 400-879 to be in increments of 4 or 6."
			);
		item.hasCorrectIncrementsForMDTIU7SEFBLU() &&
			errorsArr.push(
				"Please change the quantity of MDTIU7SEFBLU to be in increments of 24 (This item comes by the dozen and must be order in increments of 2)."
			);
		item.hasCorrectIncrementsForMDTTB4C22WHIR() &&
			errorsArr.push(
				"Please change the quantity of MDTTB4C22WHIR to be in increments of 48."
			);

		return errorsArr;
	}

	/**
	 * Run validations on a Sales Order transaction object
	 *
	 * @param {import("../../Classes/spl_transaction_record").SalesOrderTransactionRecord} transaction Transaction object
	 * @returns {{messages: string[], hasError: boolean}} Errors and warning messages
	 */
	function runSalesOrderValidations(transaction) {
		const errorsArr = [];
		const warningsArr = [];

		const existingSalesOrder = transaction.getDuplicateSalesOrder();
		existingSalesOrder &&
			errorsArr.push(
				`${existingSalesOrder} has already been created for PO# ${transaction.poNumber}. Please correct.`
			);
		transaction.hasToBeCorrected() &&
			errorsArr.push("Please correct sales order before approving.");
		transaction.dontUseCustomerInNewOrders &&
			errorsArr.push(
				`${transaction.customerName} has been disabled for new sales orders. Please use a different customer.`
			);
		transaction.needFacilityInfo &&
			errorsArr.push(
				'Please confirm the facility name and address and UNCHECK "Need Facility Info".'
			);
		transaction.creditLimitObj &&
			transaction.isCreditLimitExceeded() &&
			warningsArr.push(
				`This sales order exceeds the customer${
					transaction.creditLimitObj.creditAndBalanceByParent ? "'s parent" : ""
				}` +
					` credit limit of $${
						transaction.creditLimitObj.creditLimit
							? transaction.creditLimitObj.creditLimit
							: 0
					}`
			);

		return {
			messages: errorsArr.concat(warningsArr),
			hasError: errorsArr.length > 0,
		};
	}

	/**
	 * Run validations on a Sales Order transaction object with the item object
	 *
	 * @param {import("../../Classes/spl_transaction_record").SalesOrderTransactionRecord} transaction Transaction object
	 * @param {import("../../Classes/spl_item_sublist_record").SalesOrderItemSublistRecord} item Item object
	 * @returns {{messages: string[], hasError: boolean}} Errors and warning messages
	 */
	function runSalesOrderWithItemValidations(transaction, item) {
		const infiniteCareCustomerId = "4302";
		const beaconCustomerId = "8137";
		const petersonRehabCustomerId = "279620";
		const west60CustomerId = "5214";

		const errorsArr = [];
		const warningsArr = [];

		transaction.customerParentObj &&
			transaction.customerParentObj.id === infiniteCareCustomerId &&
			item.isGloveNotUsingCorrectRate() &&
			warningsArr.push(
				"The price of gloves for Infinite Care has changed to $18. Please update."
			);
		transaction.customerId === west60CustomerId &&
			item.isNotForDropShip() &&
			errorsArr.push(
				`${item.itemName} should not be drop-shipped. Please change.`
			);

		((transaction.customerParentObj &&
		  (transaction.customerParentObj.id === infiniteCareCustomerId || 
		   transaction.customerParentObj.id === beaconCustomerId)) || 
		 (transaction.customerId === petersonRehabCustomerId)) &&
			item.isPpeGloveItem() &&
			warningsArr.push(
				`Memo to Warehouse - Only use cases of 1,000. Item: ${item.itemName}.`
			);

		//FIXME: Should only be add the warning error, setting the param should be done before.
		transaction.parentIsFreeFreightEligible &&
			transaction.freeFreightItems.find(
				(itemObj) => itemObj.id === item.itemId
			) &&
			warningsArr.push(`This customer gets free freight on ${item.itemName}.`);

		return {
			messages: errorsArr.concat(warningsArr),
			hasError: errorsArr.length > 0,
		};
	}

	return {
		getTransactionObjForValidateLine,
		getItemObjForValidateLine,
		getTransactionObjForSave,
		getIFTransactionObjForSave,
		getItemObjForSave,
		getIFItemObjForSave,
		runItemValidations,
		runSalesOrderItemValidations,
		runItemIncrementValidations,
		runSalesOrderValidations,
		runSalesOrderWithItemValidations,
	};
});
