/**
 * @description Factory class to handle price deltas for price changes at an item level
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
  "exports",
  "require",
  "N/log",
  "N/query",
  "N/file",
  "N/record",
  "N/task",
  "../../../Classes/vlmd_custom_error_object",
], (/** @type {any} */ exports, /** @type {any} */ require) => {
  const log = require("N/log");
  const query = require("N/query");
  const file = require("N/file");
  const record = require("N/record");
  const task = require("N/task");

  /** @type {import("../../../Classes/vlmd_custom_error_object").CustomErrorObject} */
  const CustomErrorObject = require("../../../Classes/vlmd_custom_error_object");

  /**
   * Item Price Changes Class
   *
   * @class
   */
  class ItemPriceChangesFactory {
    constructor() {
      /** @type {typeof CustomErrorObject} */
      this.customErrorObject = new CustomErrorObject();

      this.queryString = "";

      this.itemObj = {
        id: null,
        name: null,
      };

      this.newPriceObjsArr = [];

      this.taskId = null;
    }

    /**
     * Loads the query string for items that have a price or price group change within the given time period
     *
     * @returns {string} Query string}
     */
    getItemsWithPriceChangesQueryString(numberOfDaysBack, itemId) {
      try {
        this.queryString = /*sql*/ `
            WITH recent_notes AS (
                SELECT DISTINCT recordid
                FROM SystemNote
                WHERE field IN ('INVTITEM.KPRICINGGROUP', 'INVTITEM.PRICELIST', 'INVTITEM.RUNITPRICE')
                  AND DATE >= (SYSDATE -  ${numberOfDaysBack ?? 1} )
            )
            SELECT
                item.id itemId,
                item.itemid itemName
            FROM
                item
                JOIN itemSubsidiaryMap ON item.id = itemSubsidiaryMap.item
                JOIN Subsidiary ON itemSubsidiaryMap.subsidiary = Subsidiary.id
                JOIN recent_notes rn ON rn.recordid = item.id
            WHERE
                Subsidiary.name = 'Supplyline Medical'
                AND item.isOnline = 'T'
                --AND (item.matrixtype IS NULL OR item.matrixtype = 'PARENT')
                AND item.itemtype IN ('InvtPart', 'NonInvtPart')
                ${itemId ? `AND item.id = ${itemId}` : ""}
       `;
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.INVALID_SEARCH,
          summary: "ERROR_LOADING_QUERY_STRING",
          details: `Error loading parent customers with price changes query string: ${err}`,
        });
      }
    }

    /**
     * Sets the class instance values based on the parsed item row
     *
     * @param {any} parsedItemRow
     * @returns {any}
     */
    setItemValuesForIteration(parsedItemRow) {
      this.itemObj = {
        id: parsedItemRow[0],
        name: parsedItemRow[1],
      };
    }

    /**
     * Push new price objects to the newPriceObjsArr
     *
     * @returns {void}
     */
    setNewPrices() {
      log.audit(`Getting new customer prices for ${this.itemObj.name}...`);
      try {
        let priceQuery = /*sql*/ `
            SELECT 
              customeritempricing.item AS item_id,
              BUILTIN.DF (customeritempricing.item) AS sku,
              'Item Pricing' AS pricetype,
              customeritempricing.customer AS customer_id,
              customeritempricing.price AS price 
            FROM
              customeritempricing 
              INNER JOIN
                  item 
                  ON item.id = customeritempricing.item 
                  AND item.isinactive = 'F' 
                  AND item.isOnline = 'T' 
              INNER JOIN
                  Customer c 
                  ON c.id = customeritempricing.customer 
            WHERE
              item.id = ${this.itemObj.id} 
              and c.custentity_is_active_this_year = 'T' 
              AND c.isInactive = 'F' 
              AND c.parent IS NULL 
              
            UNION

            SELECT 
              pricingwithcustomers.item AS item_id,
              BUILTIN.DF (pricingwithcustomers.item) AS sku,
              'Group Pricing' AS pricetype,
              pricingwithcustomers.customer AS customer_id,
              pricingwithcustomers.unitprice AS price 
            FROM
              pricingwithcustomers 
              INNER JOIN
                  item 
                  ON item.id = pricingwithcustomers.item 
                  AND item.isinactive = 'F' 
                  AND item.isOnline = 'T' 
              INNER JOIN
                  Customer c 
                  ON c.id = pricingwithcustomers.customer 
            WHERE
              item.id = ${this.itemObj.id} 
              and c.custentity_is_active_this_year = 'T' 
              AND c.isInactive = 'F' 
              AND c.parent IS NULL 
              AND pricingwithcustomers.assignedpricelevel = 'T' 
              AND pricingwithcustomers.unitprice != 0`;

        let sqlPageSize = 5000;

        let paginatedRowBegin = 1;
        let paginatedRowEnd = 5000;

        this.newPriceObjsArr = [];
        let moreRecords = true;

        do {
          let paginatedSQL = /*sql*/ `
            SELECT * FROM ( 
                SELECT ROWNUM AS ROWNUMBER, * FROM (  ${priceQuery} ) 
            ) WHERE ( ROWNUMBER BETWEEN ${paginatedRowBegin} AND ${paginatedRowEnd} )`;

          let queryResults = query
            .runSuiteQL({ query: paginatedSQL, params: [] })
            .asMappedResults();

          this.newPriceObjsArr.push(...queryResults);

          if (queryResults.length < sqlPageSize) {
            moreRecords = false;
          }

          paginatedRowBegin += sqlPageSize;
          paginatedRowEnd += sqlPageSize;
        } while (moreRecords);

        if (this.newPriceObjsArr.length === 0) {
          throw this.customErrorObject.updateError({
            errorType: this.customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
            summary: "NO_NEW_PRICES_FOUND",
            details: `No new prices found for ${this.itemObj.name}`,
          });
        }

        log.audit(
          `${this.itemObj.name} | ${this.newPriceObjsArr.length} New Price(s)`
        );
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.INVALID_SEARCH,
          summary: "ERROR_GETTING_NEW_PRICES",
          details: `Error getting new price objs arr: ${err}`,
        });
      }
    }

    /**
     * Create a new delta record for the customer
     *
     * @returns {void}
     */
    createDeltaRecords() {
      try {
        this.newPriceObjsArr.forEach((itemObj) => {
          const priceDeltaRecord = record.create({
            type: "customrecord_dlta_prc_per_itm_per_cstmr",
          });

          priceDeltaRecord.setValue({
            fieldId: "custrecord_item",
            value: itemObj.item_id,
          });

          priceDeltaRecord.setValue({
            fieldId: "custrecord_price",
            value: itemObj.price,
          });

          priceDeltaRecord.setValue({
            fieldId: "custrecord_price_type",
            value: itemObj.pricetype,
          });

          priceDeltaRecord.setValue({
            fieldId: "custrecord_customer",
            value: itemObj.customer_id,
          });

          let subRecordId = priceDeltaRecord.save();

          log.audit(
            `${this.itemObj.name} | Customer Internal ID: ${itemObj.customer_id}`,
            `Record ID: ${subRecordId}\nRecord Link: https://5802576.app.netsuite.com/app/common/custom/custrecordentry.nl?rectype=3224&id=${subRecordId}`
          );
        });
      } catch (err) {
        throw this.customErrorObject.updateError({
          errorType: this.customErrorObject.ErrorTypes.RECORD_NOT_SAVED,
          summary: "ERROR_CREATING_DELTA_RECORD",
          details: `Error creating delta record: ${err}`,
        });
      }
    }
  }

  exports.ItemPriceChangesFactory = ItemPriceChangesFactory;
});
