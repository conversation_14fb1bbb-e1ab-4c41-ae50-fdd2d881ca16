/**
 * @description Class to convert files to BAI and upload to correct folders
*
 * @NApiVersion 2.1
 */
define(['N/sftp', 'N/log'], function(sftp, log) {
    /**
     * BAI File Converter Class
     * Handles conversion of CSV files to BAI format and manages BAI files on SFTP
     */
    class BaiConverter {
        constructor(sftpConfig, timeThresholdMinutes) {
            this.SFTP_CONFIG = sftpConfig;
            this.sftp = sftp;
            this.log = log;
            
            this.PATHS = {
                ROOT: "/",
                POPULAR_BANK: "PopularBank/",
                CONVERT_TO_BAI: "ConvertToBAI/"
            };
            
            // Define BAI transaction codes
            this.BAI_TRANS_CODES = {
                CREDIT: '399', // Credit
                DEBIT: '699'   // Debit
            };
        }

        /**
         * Create an SFTP connection
         * 
         * @param {string} directory - Directory to connect to
         * @returns {Object} SFTP connection
         */
        createSftpConnection(directory) {
            try {
                return this.sftp.createConnection({
                    username: this.SFTP_CONFIG.username,
                    passwordGuid: this.SFTP_CONFIG.passwordGuid,
                    url: this.SFTP_CONFIG.url,
                    directory: directory,
                    hostKey: this.SFTP_CONFIG.hostKey,
                    port: this.SFTP_CONFIG.port
                });
            } catch (error) {
                this.log.error('Error creating SFTP connection', error);
                throw error;
            }
        }

        /**
         * Format data according to BAI file specifications
         * 
         * @param {Array} rows - Array of row objects
         * @param {String} fileName - Original file name
         * @returns {String} - BAI formatted content
         * @throws {Error} If formatting fails
         */
        formatBAIContent(rows, fileName) {
            if (!rows || rows.length === 0) {
                throw new Error('No data to process');
            }

            let baiContent = '';
            let totalAmount = 0;
            
            // Get account number from filename if it starts with "Chase"
            let accountNumber;
            
            if (fileName && fileName.toString().toLowerCase().indexOf('chase') === 0) {
                // Log the filename for debugging
                this.log.debug('Processing Chase filename', fileName);
                
                // Extract digits after "Chase" with more robust regex
                const match = fileName.toString().match(/Chase(\d+)/i);
                if (match && match[1]) {
                    accountNumber = match[1];
                    this.log.audit('Account Number from Filename', `Extracted account number ${accountNumber} from filename ${fileName}`);
                } else {
                    this.log.error('Failed to extract account number', `Could not extract account number from filename ${fileName}`);
                }
            }
            
            // If account number wasn't found in filename, get it from the data
            if (!accountNumber) {
                this.log.debug('Looking for account number in data', JSON.stringify(rows[0]));
                accountNumber = rows[0].Account || rows[0]['Account #'] || rows[0]['Account Number'];
                
                if (accountNumber) {
                    this.log.audit('Account Number from Data', `Using account number ${accountNumber} from data`);
                } else {
                    this.log.error('Account number not found in data', JSON.stringify(rows[0]));
                }
            }
            
            // Process the account number: remove negative sign and leading x's
            if (accountNumber) {
                // Convert to string if it's not already
                accountNumber = accountNumber.toString();
                
                // Remove negative sign if present
                if (accountNumber.startsWith('-')) {
                    accountNumber = accountNumber.substring(1);
                    this.log.debug('Removed negative sign', `Account number after removing negative: ${accountNumber}`);
                }
                
                // Remove leading x's (case insensitive)
                const originalAccount = accountNumber;
                accountNumber = accountNumber.replace(/^[xX]+/, '');
                
                if (originalAccount !== accountNumber) {
                    this.log.debug('Removed leading Xs', `Account number changed from ${originalAccount} to ${accountNumber}`);
                }
            }
            
            if (!accountNumber) {
                throw new Error(`Account number is required but not found in data or filename: ${fileName}`);
            }
            
            const today = new Date();
            const fileDate = today.getFullYear().toString().slice(-2) + 
                            (today.getMonth() + 1).toString().padStart(2, '0') + 
                            today.getDate().toString().padStart(2, '0');
            const fileTime = today.getHours().toString().padStart(2, '0') + 
                            today.getMinutes().toString().padStart(2, '0');
            
            baiContent += '01,7797,POPULAR,,' + fileDate + ',' + fileTime + ',,,1/\n';
            
            baiContent += '02,7797,,,' + fileDate + ',' + fileTime + ',,,1/\n';
            
            baiContent += '03,' + accountNumber + ',USD,010,/\n';
            
            let transactionCount = 0;
            rows.forEach(row => {
                if (!row.Debit && !row.Credit) return;
                
                const amount = row.Debit ? parseFloat(row.Debit) : (row.Credit ? parseFloat(row.Credit) : 0);
                if (isNaN(amount) || amount === 0) return;
                
                totalAmount += amount;
                transactionCount++;
                
                const transCode = row.Debit ? this.BAI_TRANS_CODES.DEBIT : this.BAI_TRANS_CODES.CREDIT;
                const formattedAmount = Math.abs(amount).toFixed(2).replace('.', ''); // Remove decimal point
                
                baiContent += '16,' + transCode + ',' + formattedAmount + ',,,';
                
                if (row.CheckRef) {
                    baiContent += row.CheckRef;
                }
                
                baiContent += ',/\n';
                
                if (row.Description) {
                    baiContent += '88,' + row.Description.substring(0, 80) + '/\n'; // Limit description length
                }
            });
            
            const formattedTotal = Math.abs(totalAmount).toFixed(2).replace('.', '');
            baiContent += '49,' + formattedTotal + ',1,/\n';
            
            baiContent += '98,' + formattedTotal + ',1,1/\n';
            
            baiContent += '99,' + formattedTotal + ',1,1/\n';
            
            return baiContent;
        }

        /**
         * Parse CSV content into an array of objects with robust handling
         * 
         * @param {string} csvContent - Raw CSV content
         * @returns {Array} Array of objects representing CSV rows
         */
        parseCSV(csvContent) {
            try {
                // Use a more robust approach for CSV parsing
                const rows = [];
                const lines = csvContent.split(/\r?\n/);
                
                if (lines.length === 0) return rows;
                
                const headers = this.parseIndividualCsvLine(lines[0]);
                
                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;
                    
                    const values = this.parseIndividualCsvLine(line);
                    if (values.length !== headers.length) {
                        // Instead of skipping the row, pad it with empty values if it has fewer columns
                        if (values.length < headers.length) {
                            while (values.length < headers.length) {
                                values.push('');
                            }
                        } else {
                            // If there are more values than headers, truncate the extra values
                            this.log.error(`CSV row ${i+1} has more columns than expected. Truncating extra values.`);
                            values = values.slice(0, headers.length);
                        }
                    }
                    
                    const rowObj = {};
                    headers.forEach((header, index) => {
                        rowObj[header] = values[index] || '';
                    });
                    
                    rows.push(rowObj);
                }
                
                return rows;
            } catch (error) {
                this.log.error('Error parsing CSV', error);
                throw error;
            }
        }

        /**
         * Parse a single CSV line handling quoted fields and special characters
         * 
         * @param {string} line - CSV line to parse
         * @returns {Array} Array of field values
         */
        parseIndividualCsvLine(line) {
            const result = [];
            let current = '';
            let inQuotes = false;
            
            for (let i = 0; i < line.length; i++) {
                const char = line[i];
                
                if (char === '"') {
                    if (inQuotes && i < line.length - 1 && line[i + 1] === '"') {
                        current += '"';
                        i++;
                    } else {
                        inQuotes = !inQuotes;
                    }
                } else if (char === ',' && !inQuotes) {
                    result.push(current.trim());
                    current = '';
                } else {
                    current += char;
                }
            }
            
            result.push(current.trim());
            
            return result;
        }

        /**
         * Process a CSV file and convert it to BAI format
         * 
         * @param {Object} csvFile - CSV file object with content and name
         * @returns {Object} - BAI file content
         */
        processCSVFile(csvFile) {
            try {
                const rows = this.parseCSV(csvFile.content);
                if (rows.length === 0) {
                    throw new Error('No data found in CSV file');
                }
                
                // Pass the filename to formatBAIContent
                const baiContent = this.formatBAIContent(rows, csvFile.name);
                
                return {
                    fileName: csvFile.name,
                    content: baiContent
                };
            } catch (error) {
                this.log.error(`Error processing CSV file ${csvFile.name}`, error);
                throw error;
            }
        }
    }
    
    return BaiConverter;
})
