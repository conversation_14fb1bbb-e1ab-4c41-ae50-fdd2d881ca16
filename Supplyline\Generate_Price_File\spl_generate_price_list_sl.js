/**
* @NApiVersion 2.1
* @NScriptType Suitelet
* @NModuleScope Public

* @description a SL to generate price lists on demand
* <AUTHOR>
*/

/**Define global variables, needs to be var vs. let for scope */
var scriptURL,
  parentOptions,
  productCategoryOptions,
  log,
  query,
  file,
  render,
  runtime,
  serverWidget,
  url,
  email;

define([
  "require",
  "N/log",
  "N/query",
  "N/file",
  "N/render",
  "N/runtime",
  "N/ui/serverWidget",
  "N/url",
  "N/email",
], main);

function main(require, logModule, queryModule, fileModule) {
  log = logModule;
  query = queryModule;
  file = fileModule;
  render = require("N/render");
  runtime = require("N/runtime");
  serverWidget = require("N/ui/serverWidget");
  url = require("N/url");
  email = require("N/email");

  return {
    onRequest: function (context) {
      /**Build a URL to redirect to this page*/
      scriptURL = url.resolveScript({
        scriptId: runtime.getCurrentScript().id,
        deploymentId: runtime.getCurrentScript().deploymentId,
        returnExternalURL: false,
      });

      if (context.request.method == "GET") {
        handleGetRequest(context);
      } else {
        handlePostRequest(context);
      }
    },
  };
}

function actionsView() {
  return /*html*/ `
<div  id="actionsViewDiv" class="row justify-content-md-center">
	<div id='previewResultsButtonDiv' class='.col-3 mx-2'>
		<button type="button" class="btn btn-success"  onclick="querySubmit()" accesskey="r">Preview Results</button>	
	</div>

	<div id='printPdfButtonDiv' class='.col-3 mx-2'>
		<button type="button" id = "pdf" class="btn btn-success" onclick="generateFile(this.id)" accesskey="g">Print PDF</button>
	</div>

	<div id='generateCsvButtonDiv' class='.col-3 mx-2'>
		<button type="button" id = "csv" class="btn btn-success" onclick="generateFile(this.id)" accesskey="g">Generate CSV</button>
	</div>


	<div id='emailFileButtonDiv' class='.col-3 mx-2'>
		<button type="button" id ="email" class="btn btn-success"  onclick="generateFile(this.id)" accesskey="r">Email File to Me</button>	
	</div>

</div> 
`;
}

function chooseParametersView() {
  return /*html*/ `
<div  id="chooseParametersViewDivRow1" class="row justify-content-md-center">
		${subsidiarySelect()}
		${parentCustomerSelect()}
		${productCategorySelect()}
</div> 
<div  id="chooseParametersViewDivRow2" class="row justify-content-md-center">
		${tierSelect()}
		${dateSelect()}
</div> 
`;
}

function dateSelect() {
  return /*html*/ `
	<div id='dateDiv' class='col'>
		<h2><label for="datePicker">Only include items ordered since (found on quote/sales orders)</label></h2>		
		<div class="input-daterange input-group" id="datePicker">
			<span class="input-group-text">
				<i class="bi bi-calendar-date"></i>
			</span>
			<input type="text" 
				class="input form-control" 
				name="sinceDate" 
				placeholder="MM/DD/YYYY" 
				id="sinceDate">
		</div>
		<br /> 
	</div>
`;
}

function documentSubmit(context, requestPayload) {
  try {
    let records;

    var responsePayload;
    var sessionScope = runtime.getCurrentSession();
    var paramsJsonObjInSession = sessionScope.get({ name: "paramsJsonObj" });

    if (requestPayload.paramsJsonObj === paramsJsonObjInSession) {
      records = JSON.parse(sessionScope.get({ name: "records" }));
    } else {
      records = new Array();
      var sqlQuery = getQueryForQueryParams(requestPayload.paramsJsonObj);
      log.audit("sqlQuery", sqlQuery);

      records = query
        .runSuiteQL({
          query: sqlQuery,
        })
        .asMappedResults();

      sessionScope.set({
        name: "records",
        value: JSON.stringify(records),
      });

      sessionScope.set({
        name: "paramsJsonObj",
        value: requestPayload.paramsJsonObj,
      });
    }

    sessionScope.set({
      name: "suiteQLDocumentInfo",
      value: JSON.stringify(requestPayload),
    });

    responsePayload = { submitted: true };
  } catch (e) {
    log.error({ title: "queryExecute Error", details: e });

    responsePayload = { error: e };
  }

  context.response.write(JSON.stringify(responsePayload, null, 5));
}

function generateFile(context) {
  try {
    var sessionScope = runtime.getCurrentSession();

    var records = JSON.parse(sessionScope.get({ name: "records" }));

    var docInfo = JSON.parse(sessionScope.get({ name: "suiteQLDocumentInfo" }));
    var recordsDataSource = { records: records };

    var renderer = render.create();
    renderer.templateContent = file.load(9888810).getContents();

    renderer.addCustomDataSource({
      alias: "results",
      format: render.DataSource.OBJECT,
      data: recordsDataSource,
    });

    if (docInfo.docType == "pdf") {
      let renderObj = renderer.renderAsPdf();
      let pdfString = renderObj.getContents();
      context.response.setHeader("Content-Type", "application/pdf");
      context.response.write(pdfString);
    } else if (docInfo.docType == "csv") {
      const columnNames = Object.keys(records[0]);
      const productLinkIndex = columnNames.findIndex(
        (col) => col.toLowerCase() === "product_link"
      );

      let priceString = columnNames.join(",") + "\n";
      priceString += records
        .map((obj) => {
          return Object.entries(obj)
            .map(([key, val], index) => {
              // Check if this is the product_link column and format it as a clickable link
              if (
                productLinkIndex !== -1 &&
                index === productLinkIndex &&
                val
              ) {
                // Extract URL from HTML anchor tag
                const urlMatch = val.toString().match(/href="([^"]+)"/);
                if (urlMatch && urlMatch[1]) {
                  // Format as Excel HYPERLINK formula
                  return `"=HYPERLINK(""${urlMatch[1]}"",""Click here to view item"")"`;
                }
              }
              return `"${
                val?.toString().replace(/"/g, "").replace(/,/g, "") ?? ""
              }"`;
            })
            .join(",");
        })
        .join("\n");

      var fileCsv = file.create({
        name: `${docInfo.parentCustomer}_Price_List.csv`,
        fileType: file.Type.CSV,
        contents: priceString,
        encoding: file.Encoding.UTF_8,
      });

      context.response.writeFile(fileCsv);
    } else if (docInfo.docType == "email") {
      let priceString =
        Object.keys(records[0]).join(",") +
        "\n" +
        records
          .map((obj) =>
            Object.values(obj)
              .map((val) => `"${val?.toString().replace(/,/g, "")}"`)
              .join(",")
          )
          .join("\n");

      var fileCsv = file.create({
        name: `${docInfo.parentCustomer}_Price_List.csv`,
        fileType: file.Type.CSV,
        contents: priceString,
        encoding: file.Encoding.UTF_8,
      });

      email.send({
        author: 223244, //Requests
        recipients: runtime.getCurrentUser().id,
        subject: `${docInfo.parentCustomer}_Price_List`,
        body: "Please find the price file attached.",
        attachments: [fileCsv],
      });

      context.response.write(
        "Your file is on its way! Look out in your inbox."
      );
    } else {
      let htmlString = renderer.renderAsString();
      context.response.setHeader("Content-Type", "text/html");
      context.response.write(htmlString);
    }
  } catch (e) {
    log.error({ title: "generateFile Error", details: e });

    context.response.write("Error: " + e);
  }
}

function generateHTML() {
  return /*html*/ `

	 <!--jQuery-->
	 <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous"></script>
	 <!--Popper--> 
	 <script src="https://cdn.jsdelivr.net/npm/popper.js@1.14.7/dist/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous"></script>
	 <!--Bootstrap--> 
	 <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous"></script>
	   <!--Bootstrap-DatePicker-->
	 <script src="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/js/bootstrap-datepicker.min.js"></script>
	 <!--DataTables-->
	 <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.js"></script>
	 
	 <!--Bootstrap CSS-->
	 <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
	 <!--Bootstrap-DatePicker CSS-->
	 <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/css/bootstrap-datepicker.min.css">
	 <!--DataTables CSS-->
	 <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/jquery.dataTables.css">
	 <!--Icons CSS-->
	 <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.5.0/font/bootstrap-icons.css">

	<style type="text/css">
		input[type="text"],
		input[type="search"],
		textarea,
		button {
			outline: none;
			box-shadow: none !important;
			border: 1px solid #ccc !important;
		}

		p,
		pre {
			font-size: 10pt;
		}

		td,
		th {
			font-size: 10pt;
			border: 3px;
		}

		th {
			text-transform: lowercase;
			font-weight: bold;
		}

		.input-group-text {
			background-color: transparent;
			border: none;
		}

		.input-group-text i {
			color: #6c757d;
			font-size: 1.1rem;
		}
	</style>

		${htmlUI()}

	<script>
		var
			queryResponsePayload,
			fileLoadResponsePayload;

		window.jQuery = window.$ = jQuery;

		$(document).ready(function(){
			$('#datePicker').datepicker({
				format: 'mm/dd/yyyy',
				autoclose: true,
				todayHighlight: true,
				clearBtn: true,
				orientation: "auto"
			});
		});

		$('#queryUI').show();

		${jsFunctionQuerySubmit()}
		${jsFunctionResponseGenerate()}

		${jsFunctionResponseGenerateTable()}
		${jsFunctionGenerateFile()}
	</script>

`;
}

function getParentObjs() {
  let sqlQuery = /*sql*/ `
	SELECT
		customer.id,
		customer.companyname name,
	FROM
		customer 
	JOIN
		customersubsidiaryrelationship 
	ON
		customer.id = customersubsidiaryrelationship.entity 
	WHERE
		customersubsidiaryrelationship.subsidiary = 1 
		AND customer.parent IS NULL
	ORDER BY
		customer.companyname`;

  return query
    .runSuiteQL({
      query: sqlQuery,
    })
    .asMappedResults();
}

function getPriceListForQueryParams(context, requestPayload) {
  try {
    var responsePayload;
    let records;

    var sessionScope = runtime.getCurrentSession();

    var paramsJsonObjInSession = sessionScope.get({ name: "paramsJsonObj" });

    if (requestPayload.paramsJsonObj === paramsJsonObjInSession) {
      records = JSON.parse(sessionScope.get({ name: "records" }));
    } else {
      records = new Array();
      var sqlQuery = getQueryForQueryParams(requestPayload.paramsJsonObj);

      records = query
        .runSuiteQL({
          query: sqlQuery,
        })
        .asMappedResults();

      let sqlPageSize = 5000;

      let paginatedRowBegin = 1;
      let paginatedRowEnd = 5000;

      let moreRecords = true;

      do {
        let paginatedSQL = /*sql*/ `
            SELECT * FROM ( 
                SELECT ROWNUM AS ROWNUMBER, * FROM (  ${sqlQuery} ) 
            ) WHERE ( ROWNUMBER BETWEEN ${paginatedRowBegin} AND ${paginatedRowEnd} )`;

        let queryResults = query
          .runSuiteQL({ query: paginatedSQL, params: [] })
          .asMappedResults();

        records.push(...queryResults);

        if (queryResults.length < sqlPageSize) {
          moreRecords = false;
        }

        paginatedRowBegin += sqlPageSize;
        paginatedRowEnd += sqlPageSize;
      } while (moreRecords);

      sessionScope.set({
        name: "records",
        value: JSON.stringify(records),
      });

      sessionScope.set({
        name: "paramsJsonObj",
        value: requestPayload.paramsJsonObj,
      });
    }

    responsePayload = { records };
  } catch (e) {
    log.error({ title: "generateHTMLPriceFile Error", details: e });

    responsePayload = { error: e };
  }

  context.response.write(JSON.stringify(responsePayload, null, 5));
}

function getQueryForQueryParams(paramsJsonObj) {
  try {
    let {
      subsidiaryId,
      parentCustomerId,
      tierId,
      productCategoryId,
      sinceDate,
    } = JSON.parse(paramsJsonObj);

    if (tierId) {
      let priceTierQuery = /*sql*/ `
				SELECT DISTINCT
					item.itemid item_name,
					item.vendorname vendor_name,
					item.displayname item_description,
					BUILTIN.DF(item.class) item_category,
					BUILTIN.DF(item.saleunit) AS item_uom,
					BUILTIN.DF(pricing.pricelevel) price_level,
					itemVendor.purchasePrice purchase_price,
					pricing.unitprice item_price,
					CASE 
						WHEN item.custitem_spl_product_link IS NOT NULL THEN
					'<a href="'|| item.custitem_spl_product_link || '" target="_new">View Item</a>'
					ELSE '' END product_link,
				FROM
					customrecord_spl_pricing_group_by_tier tier 
				JOIN
					item 
					ON item.pricinggroup = tier.custrecord_pricing_group 
					--AND item.custitem_spl_apprvd_for_itm_ctlg = 'T' 
					AND item.isinactive = 'F' 
				JOIN
					itemSubsidiaryMap 
					ON item.id = itemSubsidiaryMap.item 
					AND itemSubsidiaryMap.subsidiary = 1 
				JOIN
					pricing 
					ON pricing.item = item.id 
					AND pricing.pricelevel = tier.custrecord_tier_level_price_level_${tierId}
				LEFT JOIN itemVendor
					ON itemVendor.item = item.id and itemVendor.preferredVendor =  'T'
						${
              productCategoryId
                ? `
				JOIN classification 
					ON item.class = classification.id 
				JOIN
					classification parentCategory 
					ON classification.parent = parentCategory.id`
                : ""
            }
					${
            sinceDate
              ? `
				JOIN
					transactionLine 
					ON transactionLine.item = item.id 
				JOIN
					transaction 
					ON transaction.id = transactionLine.transaction `
              : ""
          }
				WHERE 1=1
					${
            productCategoryId
              ? `
					AND (
						item.class = ${productCategoryId}
						OR classification.parent = ${productCategoryId}
						OR parentCategory.parent = ${productCategoryId}
					)`
              : ""
          }			
					${
            sinceDate
              ? `
					AND transaction.type IN 
						(
							'Estimate',
							'SalesOrd' 
						)
					AND transaction.createdDate > = TO_DATE( '${sinceDate}', 'MM/DD/YYYY' )`
              : ""
          }`;

      return priceTierQuery;
    }

    parentCustomerId =
      query
        .runSuiteQL({
          query: `	SELECT	  
		custrecord_spl_prchsng_fclty 
FROM
	customrecord_vlmd_edi_integration 
WHERE
	BUILTIN.MNFILTER( custrecord_edi_intgrtn_prnt_fclty, 'MN_INCLUDE', '', 'FALSE', NULL, '${parentCustomerId}' ) = 'T' 
	AND custrecord_spl_prchsng_fclty is not null`,
        })
        .asMappedResults()[0]?.["custrecord_spl_prchsng_fclty"] ??
      parentCustomerId;

    let priceQuery = /*sql*/ `
			SELECT DISTINCT
				customer.companyname customer,
				item.itemid item_name,
				item.displayname item_description,
				item.vendorname vendor_name,
				BUILTIN.DF(item.class) item_category,
				BUILTIN.DF(item.saleunit) AS item_uom,   
				BUILTIN.DF(customeritempricing .level) price_level,
				itemVendor.purchasePrice purchase_price,
				customeritempricing.price AS item_price,
				CASE 
					WHEN item.custitem_spl_product_link IS NOT NULL THEN
				'<a href="'|| item.custitem_spl_product_link || '" target="_new">View Item</a>'
				ELSE '' END product_link,
			FROM
				customeritempricing 
			INNER JOIN
				item 
				ON item.id = customeritempricing.item 
				--AND item.custitem_spl_apprvd_for_itm_ctlg = 'T' 
				AND item.isinactive = 'F' 
			JOIN
				customer 
				ON customer.id = customeritempricing.customer 
				${
          productCategoryId
            ? `
			JOIN
				classification 
				ON item.class = classification.id 
			JOIN
				classification parentCategory 
				ON classification.parent = parentCategory.id`
            : ""
        }
				${
          sinceDate
            ? `
			JOIN
				transactionLine 
				ON transactionLine.item = item.id 
			JOIN
				transaction 
				ON transaction.id = transactionLine.transaction `
            : ""
        }
			JOIN itemVendor
				ON itemVendor.item = item.id and itemVendor.preferredVendor =  'T'
			WHERE
				customeritempricing.customer = ${parentCustomerId}
			${
        productCategoryId
          ? `
				AND (
					item.class = ${productCategoryId}
					OR classification.parent = ${productCategoryId}
					OR parentCategory.parent = ${productCategoryId}
				)`
          : ""
      }
			${
        sinceDate
          ? `
				AND transaction.type IN 
					(
						'Estimate',
						'SalesOrd' 
					)
				AND transaction.createdDate > = TO_DATE( '${sinceDate}', 'MM/DD/YYYY' )`
          : ""
      }
			UNION
			SELECT DISTINCT
				customer.companyname,
				item.itemid AS item_name,
				item.displayname,
				item.vendorname vendor_name,
				BUILTIN.DF(item.class),
				BUILTIN.DF(item.saleunit) AS unit,
				BUILTIN.DF(pricingwithcustomers.pricelevel) price_level,
				itemVendor.purchasePrice purchase_price,
				pricingwithcustomers.unitprice AS price,
				CASE 
					WHEN item.custitem_spl_product_link IS NOT NULL THEN
				'<a href="'|| item.custitem_spl_product_link || '" target="_new">View Item</a>'
				ELSE '' END product_link,
			FROM
				pricingwithcustomers 
			INNER JOIN
				item 
				ON item.id = pricingwithcustomers.item 
				--AND item.custitem_spl_apprvd_for_itm_ctlg = 'T' 
				AND item.isinactive = 'F' 
			JOIN
				customer 
				ON customer.id = pricingwithcustomers.customer 
				${
          productCategoryId
            ? `
			JOIN
				classification 
				ON item.class = classification.id 
			JOIN
				classification parentCategory 
				ON classification.parent = parentCategory.id `
            : ""
        }
			${
        sinceDate
          ? `
			JOIN
				transactionLine 
				ON transactionLine.item = item.id 
			JOIN
				transaction 
				ON transaction.id = transactionLine.transaction 
				AND transaction.createdDate > = TO_DATE( '${sinceDate}', 'MM/DD/YYYY' ) `
          : ""
      }
			JOIN itemVendor
				ON itemVendor.item = item.id and itemVendor.preferredVendor =  'T'
			WHERE
				pricingwithcustomers.customer = 	  	${parentCustomerId}  
				AND pricingwithcustomers.assignedpricelevel = 'T' 
			${
        productCategoryId
          ? `
				AND (
					item.class = ${productCategoryId}
					OR classification.parent = ${productCategoryId}
					OR parentCategory.parent = ${productCategoryId}
					)`
          : ""
      }
			${
        sinceDate
          ? `
				AND transaction.type IN 
					(
						'Estimate',
						'SalesOrd' 
					)`
          : ""
      }`;

    return priceQuery;
  } catch (e) {
    throw e;
  }
}

function getProductCategoryObjs() {
  let sqlQuery = /*sql*/ `
	SELECT
		classification.id,
		classification.name,
		classification.fullname
	FROM
		classification 
	JOIN
		classificationsubsidiarymap 
		ON classification.id = classificationsubsidiarymap.classification 
		AND classificationsubsidiarymap.subsidiary = 1 
	ORDER BY
		classification.fullname`;

  return query
    .runSuiteQL({
      query: sqlQuery,
    })
    .asMappedResults();
}

function handleGetRequest(context) {
  if (context.request.parameters.hasOwnProperty("function")) {
    /**If this is coming from a redirect, handle according to instructions sent */ if (
      context.request.parameters["function"] == "generateFile"
    ) {
      generateFile(context);
    }
  } else {
    /**Coming to this page for the first time, load the start screen */
    parentOptions = getParentObjs();
    productCategoryOptions = getProductCategoryObjs();

    var form = serverWidget.createForm({ title: `Price File Tool` });

    var htmlField = form.addField({
      id: "custpage_field_html",
      type: serverWidget.FieldType.INLINEHTML,
      label: "HTML",
    });

    htmlField.defaultValue = generateHTML();

    context.response.writePage(form);
  }
}

function handlePostRequest(context) {
  var requestPayload = JSON.parse(context.request.body);

  context.response.setHeader("Content-Type", "application/json");

  switch (requestPayload["function"]) {
    case "documentSubmit":
      return documentSubmit(context, requestPayload);
      break;
    case "getPriceListForQueryParams":
      return getPriceListForQueryParams(context, requestPayload);
      break;

    default:
      log.error({
        title: "Payload - Unsupported Function",
        details: requestPayload["function"],
      });
  }
}

function htmlUI() {
  return /*html*/ `
<div class="container" id="queryUI" style="text-align: left;">	

		${chooseParametersView()}
		${actionsView()}
		${resultsDivView()}			
		<div id="query" style="max-width: 100%; margin-top: 12px; display: none; overflow: auto; overflow-y: hidden;"></div>
</div>`;
}

function jsFunctionGenerateFile() {
  return `
		function generateFile(fileType) {	
			let subsidiaryId = document.getElementById('subsidiarySelect').value 
			let parentCustomerId = document.getElementById('parentCustomerSelect').value 
			let tierId = document.getElementById('tierSelect').value 
			let productCategoryId = document.getElementById('productCategorySelect').value 
			let sinceDate = document.getElementById('sinceDate').value 
	
		if ( parentCustomerId == '' && tierId == '') { 
			alert( 'Please choose a parent customer or a price tier.' );
			return; 
		}

		if ( parentCustomerId != '' && tierId != '') { 
			alert( 'You can not choose a parent customer and a price tier at the same time.' );
			return; 
		}

			let paramsJsonObj = JSON.stringify({
				subsidiaryId, 
				parentCustomerId,
				tierId, 
				productCategoryId,
				sinceDate
			});
	
			rowBegin = 1;
			rowEnd = 999999;
	
			var requestPayload = { 
				'function': 'documentSubmit', 
				'paramsJsonObj': paramsJsonObj,
				'parentCustomer': document.getElementById('parentCustomerSelect').value,
				'rowBegin': rowBegin,
				'rowEnd': rowEnd,
				'docType': fileType
			}
	
			var xhr = new XMLHttpRequest();
		
			xhr.open( 'POST', '${scriptURL}', true );
			
			xhr.setRequestHeader( 'Accept', 'application/json' );		
		
			xhr.send( JSON.stringify( requestPayload ) );
		
			xhr.onload = function() {
		
				if( xhr.status === 200 ) {	
					try {			
						queryResponsePayload = JSON.parse( xhr.response );						
					} catch( e ) {	
						alert( 'Unable to parse the response.' );
						return;					
					}
			
					if ( queryResponsePayload['error'] == undefined ) {				
						window.open( '${scriptURL}&function=generateFile' );
					} else {					
						alert( 'Error: ' + queryResponsePayload.error.message );									
					}																																		
				} else {				
					alert( 'Error: ' + xhr.status );									
				}
			}
	
		}`;
}

function jsFunctionQuerySubmit() {
  return `
	function querySubmit() {	
		let subsidiaryId = document.getElementById('subsidiarySelect').value 
		let parentCustomerId = document.getElementById('parentCustomerSelect').value 
		let tierId = document.getElementById('tierSelect').value 
		let productCategoryId = document.getElementById('productCategorySelect').value 
		let sinceDate = document.getElementById('sinceDate').value 
	
		if ( parentCustomerId == '' && tierId == '') { 
			alert( 'Please choose a parent customer or a price tier.' );
			return; 
		}

		if ( parentCustomerId != '' && tierId != '') { 
			alert( 'You can not choose a parent customer and a price tier at the same time.' );
			return; 
		}

		let paramsJsonObj = JSON.stringify({
			subsidiaryId, 
			parentCustomerId,
			tierId,
			productCategoryId,
			sinceDate,
		});


		document.getElementById('resultsDiv').style.display = "block";
		document.getElementById('resultsDiv').innerHTML = '<div  id="loadingDiv" class="row justify-content-md-center"><h5>'+
		'<div class="spinner-border text-success" role="status"><span class="visually-hidden"></span></div>'+
		"<div id='loadingStatusDiv'>This may take a moment while we retrieve and process the data.</div></h5></div>"

		var requestPayload = { 
			'function': 'getPriceListForQueryParams', 
			'paramsJsonObj': paramsJsonObj,
		}

		var xhr = new XMLHttpRequest();

		xhr.open( 'POST', '${scriptURL}', true );

		xhr.setRequestHeader( 'Accept', 'application/json' );		

		xhr.send( JSON.stringify( requestPayload ) );

		xhr.onload = function() {
			if( xhr.status === 200 ) {	
				try {
					queryResponsePayload = JSON.parse( xhr.response );
				} catch( e ) {	
					alert( 'Unable to parse the response.' );
					return;					
				}
		
				if ( queryResponsePayload['error'] == undefined ) {	
				document.getElementById('loadingStatusDiv').value = 'Got the results! Getting the table ready.'
					responseGenerate(); 

				} else {		
					var content = '<h5 class="text-danger">Error</h5>';
					content += '<pre>';
					content += queryResponsePayload.error.message;
					content += '</pre>';		

					console.log(content)
				}																																	
			} else {
		
				var content = '<h5 class="text-danger">Error</h5>';
				content += '<pre>';
				content += 'XHR Error: Status ' + xhr.status;
				content += '</pre>';		

				console.log(content)			
			}			
		}														
													
	}`;
}

function jsFunctionResponseGenerate() {
  return `	
	function responseGenerate() {	
		let docType = 'pdf';	

		switch ( docType) {
			case 'pdf':
				responseGenerateTable();					
				break;	
			default:								
				responseGenerateTable();

		} 	
	}	
				
`;
}

function jsFunctionResponseGenerateTable() {
  return `	
	function responseGenerateTable() {
		if ( queryResponsePayload.records.length > 0 ) {	
			let columnNames = Object.keys( queryResponsePayload.records[0] );

			let thead = '<thead class="thead-light">';
			thead += '<tr>' + columnNames.map(columnName => '<th>' + columnName + '</th>') + '</tr>';
			thead += '</thead>';
			thead = thead.split(',').join('')

			let tbody = '<tbody>';
			for ( row = 0; row < queryResponsePayload.records.length; row++ ) {		
				tbody += '<tr>';
				
				for ( i = 0; i < columnNames.length; i++ ) {
					var recordValue = queryResponsePayload.records[row][ columnNames[i] ];
					if ( recordValue === null ) {							
							tbody +='<td><span style="color: #ccc;">' + 'NULL' + '</span></td>';												
					} else if(typeof recordValue === "number"){
						tbody += '<td>'+ recordValue.toFixed(2) + '</td>';	
					}else {
						tbody += '<td>' + recordValue + '</td>';					
					}
				}		
				tbody += '</tr>';		
			}	
			tbody += '</tbody>';

			var content = ''; 
			content += 'Retrieved ' + queryResponsePayload.records.length + ' rows. <br/>';

			content += '<div class="table-responsive">';
			content += '<table class="table table-striped table-sm  table-hover table-responsive-sm" id="resultsTable">';
			content += thead;
			content += tbody;
			
			content += '</table>';
			content += '</div>';		

			document.getElementById('resultsDiv').innerHTML = content;
			$('#resultsTable').DataTable();
		} else {			
			document.getElementById('resultsDiv').innerHTML = '<h5 class="text-warning">No Pricing Was Found</h5>';
		}

	}	
		
`;
}

function parentCustomerSelect() {
  return /*html*/ `
	<div id='parentCustomerDiv' class='col'>
		<h2><label for="parentCustomerSelect">Parent Customer</label></h2>
		<select class="form-control" id="parentCustomerSelect" style="width: 100%;">
			<option selected value=""></option>
			${parentOptions.map(
        (
          parentObj
        ) => `<option value="${parentObj.id}">${parentObj.name}</option>
				`
      )}					
		</select>'
		<br /> 
	</div>
`;
}

function productCategorySelect() {
  return /*html*/ `
	<div id='productCategoryDiv' class='col'>
		<h2><label for="productCategorySelect">Product Category</label></h2>
		<select class="form-control" id="productCategorySelect" style="width: 100%;">
		<option selected value="">All</option>
		${productCategoryOptions.map((categoryObj) => {
      // Format the display name to show hierarchy
      let displayName = categoryObj.fullname;
      if (displayName.includes(":")) {
        const colonCount = (displayName.match(/:/g) || []).length;
        const indent = "&nbsp;".repeat(colonCount * 4);
        const lastPart = displayName.split(":").pop().trim();
        displayName = `${indent}└─ ${lastPart}`;
      }
      return `<option value="${categoryObj.id}">${displayName}</option>`;
    })}	
			
		</select>'
		<br /> 
	</div>
`;
}

function resultsDivView() {
  return /*html*/ `
	<tr>
		<td colspan="3">	
			<div id="resultsDiv" style="max-width: 100%; margin-top: 12px; display: none; overflow: auto; overflow-y: hidden;">
			<!-- RESULTS -->								
			</div>
		</td>
	</tr>
	`;
}

function subsidiarySelect() {
  return /*html*/ `
	<div id='subsidiaryDiv' class='col'>
		<h2><label for="subsidiarySelect">Subsidiary</label></h2>
		<select class="form-control" id="subsidiarySelect" style="width: 100%;">
			<option></option>
			<option selected value="1">SupplyLine</option>
			<option  value="">More options coming soon!</option>
		</select>'
	</div>
`;
}

function tierSelect() {
  return /*html*/ `
	<div id='tierDiv' class='col'>
		<h2><label for="tierSelect">Price Tier</label></h2>
		<select class="form-control" id="tierSelect" style="width: 100%;">
			<option selected value=""></option>
			<option value="1">Tier 1</option>
			<option value="2">Tier 2</option>
			<option value="3">Tier 3</option>
		</select>'
		<br /> 
	</div>
`;
}
