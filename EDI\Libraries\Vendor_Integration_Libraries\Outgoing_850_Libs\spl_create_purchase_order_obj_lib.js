/**
 * @NApiVersion 2.1
 */

define([
  "N/record",
  "N/query",
  "GetPurchaseOrderAddressLib",
  "Moment",
], function (record, query, getPurchaseOrderAddress, moment) {
  function getPurchaseOrderObj(
    internalId,
    getShippingAccount,
    customErrorObject
  ) {
    var errorLog = [];
    var purchaseOrderObj;

    try {
      var helperFunctions = (function () {
        function _processFail(logMessage) {
          errorLog.push(purchaseOrderNumber + ": " + logMessage);
        }

        function getPurchaseOrder() {
          try {
            return record.load({
              type: record.Type.PURCHASE_ORDER,
              id: internalId,
            });
          } catch (error) {
            _processFail(
              "Purchase order record not loaded for internal ID " + id
            );
          }
        }

        function setControlNumber() {
          try {
            var controlNumberToSlice =
              purchaseOrderNumber.replace("PO", "") +
              moment().format("MMDDYYYY");
            var controlNumber = controlNumberToSlice.slice(0, 9);
            return controlNumber;
          } catch (error) {
            _processFail(
              "Control number not set for " + purchaseOrderNumber + "."
            );
          }
        }

        function setPurchaseOrderValues() {
          var isDropShip = _checkIfDropShip();
          let customerObj = _getCustomer(isDropShip);
          var customerID = customerObj.customerIDValue;

          var shippingAddress = _getShippingAddress(isDropShip, customerID);

          try {
            return {
              isDropShip: isDropShip,
              orderType: _getOrderType(isDropShip),
              controlNumber: controlNumber,
              date: netSuitePurchaseOrder.getValue("trandate"),
              number: purchaseOrderNumber,
              pONumber: netSuitePurchaseOrder.getText("otherrefnum"),
              customer: customerObj.customerName,
              shipToAccount: _getShippingAccount(),
              address: shippingAddress,
              items: [],
              total: netSuitePurchaseOrder.getValue("amountremaining"),
            };
          } catch (err) {
            _processFail(`PO values not gotten: ${err}`);

            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
              summary: "PO_VALUES_NOT_GOTTEN",
              details: `PO values not gotten: ${err}`,
            });
          }
        }

        function _checkIfDropShip() {
          try {
            return netSuitePurchaseOrder.getValue("createdfrom") !== "";
          } catch (error) {
            _processFail(
              "Drop-ship value not gotten successfully for purchase order " +
                purchaseOrderNumber +
                "."
            );
          }
        }

        function _getOrderType(isDropShip) {
          try {
            if (isDropShip) {
              return "DS";
            }
            return "NE";
          } catch {
            _processFail(
              "Order type not gotten successfully for purchase order " +
                purchaseOrderNumber +
                "."
            );
          }
        }

        function _getCustomer(isDropShip) {
          try {
            if (isDropShip) {
              return {
                customerName: netSuitePurchaseOrder
                  .getText("shipto")
                  .replace(/^(.*):/, "")
                  .trim(),
                customerIDValue: netSuitePurchaseOrder.getValue("shipto"),
              };
            }

            return {
              customerName: "Supplyline Medical",
              customerIDValue: null,
            };
          } catch {
            _processFail(
              "Customer not gotten successfully for purchase order " +
                purchaseOrderNumber +
                "."
            );
          }
        }

        function _getShippingAccount() {
          if (getShippingAccount) {
            //CODE HERE TO ACTUALLY GET SHIPPING ACCOUNT, MAYBE DO AS EXTERNAL LIBRARAY
          } else {
            return "";
          }
        }

        function _getShippingAddress(isDropShip, customerID) {
          try {
            var address = getPurchaseOrderAddress.getPurchaseOrderAddress(
              isDropShip,
              internalId,
              purchaseOrderNumber,
              customerID
            );
          } catch (err) {
            //Assuming this error will be a string vs. obj since the lib called throws strings in its error handling
            throw customErrorObject.updateError({
              errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
              summary: "SHIPPING_ADDRESS_NOT_GOTTEN",
              details: `Error getting shipping address: ${err}`,
            });
          }

          if (!address || !address.address1) {
            _processFail(
              "UNHANDLED_ERROR: Shipping address not gotten for " +
                purchaseOrderNumber +
                "."
            );
          }

          return address;
        }

        function setItems() {
          var itemsLineCount = netSuitePurchaseOrder.getLineCount({
            sublistId: "item",
          });

          for (var x = 0; x < itemsLineCount; x++) {
            try {
              var item = {};

              item.name = netSuitePurchaseOrder.getSublistText({
                sublistId: "item",
                fieldId: "vendorname",
                line: x,
              });

              item.description = netSuitePurchaseOrder
                .getSublistText({
                  sublistId: "item",
                  fieldId: "description",
                  line: x,
                })
                .substring(0, 80);

              var quantity = netSuitePurchaseOrder.getSublistText({
                sublistId: "item",
                fieldId: "quantity",
                line: x,
              });
              item.quantity = Math.round(quantity);

              item.rate = netSuitePurchaseOrder.getSublistText({
                sublistId: "item",
                fieldId: "rate",
                line: x,
              });

              item.uom = _getParsedUOM(x, item.name);

              item.lineNumber = x + 1;

              purchaseOrderObj.items.push(item);
            } catch (error) {
              _processFail(
                "Item " +
                  item.name +
                  " not gotten correctly for purchase order " +
                  purchaseOrderNumber +
                  ". \nError: " +
                  error,
                false
              );
            }
          }

          if (purchaseOrderObj.items.length <= 0) {
            _processFail(
              "No items added for " + purchaseOrderNumber + ".",
              false
            );
          }
        }

        function _getParsedUOM(x, itemName) {
          var uomId = netSuitePurchaseOrder.getSublistValue({
            sublistId: "item",
            fieldId: "units",
            line: x,
          });

          const sqlQuery = `
                SELECT
                    abbreviation,
                    unitstype AS parentUnitId
                FROM
                    unitsTypeUom
                WHERE
                    internalid = ?`;

          const result = query
            .runSuiteQL({
              query: sqlQuery,
              params: [uomId],
            })
            .asMappedResults()[0];

          let {  abbreviation:unitAbbreviation, parentUnitId } = result;
          let parentUnitIsEach = parentUnitId == 1;

          const unitAbbreviationArr = unitAbbreviation.split("/");

          /*These lines handle the difference in naming convention of different sub-unit abbreviations.
  
                  Old items are entered into NS under the primary unit type of "Each", (internal id 1) and follow this convention:
                      6 EA / CS
  
                  All other sub-units follow the new naming convention:
                      CS/6EA
                   */

          let uom =
            (parentUnitIsEach
              ? unitAbbreviationArr.pop()
              : unitAbbreviationArr.shift()
            )?.trim() || "";

          uom = uom.replace("CS", "CA");

          if (!uom.length > 2) {
            _processFail(
              "The UOM for " +
                itemName +
                ", " +
                uom +
                ", is not 2 characters long. Please correct in order to be processed correctly.",
              false
            );
          }

          if (!uom) {
            _processFail(
              "The UOM is missing for " +
                itemName +
                ". Please add in order to be processed correctly.",
              false
            );
          }

          return uom;
        }

        return {
          getPurchaseOrder,
          setControlNumber,
          setPurchaseOrderValues,
          setItems,
        };
      })();

      var netSuitePurchaseOrder = helperFunctions.getPurchaseOrder();
      var purchaseOrderNumber = netSuitePurchaseOrder.getText("tranid");
      var controlNumber = helperFunctions.setControlNumber();
      purchaseOrderObj = helperFunctions.setPurchaseOrderValues();
      helperFunctions.setItems();
    } catch (err) {
      customErrorObject.updateError({
        errorType: customErrorObject.ErrorTypes.NO_VALUE_RETURNED,
        summary: "ERROR_IN_GET_PO_OBJ_LIB",
        details: purchaseOrderNumber ?? "Undefined PO",
      });
    }

    purchaseOrderObj.errorLog = errorLog;
    purchaseOrderObj.continueProcessing = errorLog.length <= 0;

    return purchaseOrderObj;
  }

  return {
    getPurchaseOrderObj,
  };
});
