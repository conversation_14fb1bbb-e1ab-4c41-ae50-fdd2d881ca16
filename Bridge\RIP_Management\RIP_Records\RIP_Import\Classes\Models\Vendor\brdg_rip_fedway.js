/**
 * @description Bridge RIP Vendor class for Fedway
 *
 * @NApiVersion 2.1
 * <AUTHOR>
 */

define([
  "exports",
  "require",
  "N/log",
  "./brdg_rip_vendor",
], function (/** @type {any} */ exports, /** @type {any} */ require) {
  const log = require("N/log");
  const { BridgeRIPVendor } = require("./brdg_rip_vendor");

  // Capture segments with commas enclosed in double quotes
  const COMMA_DELIMITED_SEGMENT = /(?:\"([^\"]*)\")|([^",]+)/g;
  
  // 1C $12 stands for 1 Case(s) $12
  const QUANTITY_UNIT_AMOUNT = /(\d+)([A-Z])\s*\$(\d+)/;

  /**
   * Return the unit of measure based on the abbreviation
   *
   * @param {string} uom Abbreviation
   * @returns {string} Unit of measure
   */
  const getUnitOfMeasure = (uom) => {
    switch (uom) {
      case "C":
        return "Case(s)";
      case "B":
        return "Bottle(s)";
      default:
        return "Unit(s)";
    }
  };

  /**
   * Bridge RIP Fedway Class
   */
  class BridgeRIPFedway extends BridgeRIPVendor {
    /** @param {{[key:string]: any}} props Constructor params */
    constructor(props){
      super(props);
    }

    /**
     * Split the row string from the CSV file and attach the line index
     *
     * @param {import("N/file").File} ripFile NetSuite File
     * @returns {any[]} Row strings split by commas
     */
    splitLines(ripFile) {
      const fileIterator = ripFile.lines.iterator();

      let lineCount = 0;

      // Since we iterate through the file from top to bottom, we cannot skip the header
      // Iterate over the header by returning false after reading it
      fileIterator.each((/** @type {any} */ header) => false);

      // Continue reading the file, and store the contents to a data lines array
      const /** @type {any[]} */ dataArr = [];
      fileIterator.each((/** @type {any} */ line) => {
        const lineArr = line.value
          .trim()
          .match(COMMA_DELIMITED_SEGMENT)
          .map((/** @type {string} */ field) => field.replace(/^"|"$/g, ''));
        dataArr.push([...lineArr, lineCount]);
        lineCount++;

        return true;
      });

      return dataArr;
    }

    /**
     * Create an object from the line extracted from the CSV file
     *
     * @param {any[]} fields Array of column values
     * @returns {{[key:string]: any}} Key-value pairs of column values
     */
    parseLine(fields) {
      const fieldNames = [
        "ripName", "sku", "fromDate", "toDate", "ripCode", "description", "comments", "pack",
        "countAs", "month", "family", "level", "allRips", "lineCount",
      ];
      const trimIfString = (/** @type {any} */ x) => (typeof x === "string" ? x.trim() : x);
      const values = fields.map(trimIfString);
      const rip = Object.fromEntries(fieldNames.map((key, index) => [key, values[index]]));

      // Add quantity, unit of measure and amount as properties based on the RIP Level e.g. 1C $12
      const level = rip.level.match(QUANTITY_UNIT_AMOUNT);
      rip.qty = Number(level[1] || 0);
      rip.uom = getUnitOfMeasure(level[2]);
      rip.amt = Number(level[3] || 0);

      return rip;
    }

    /**
     * Merge RIP Levels into a single row
     * - Use parseFloat to remove unit from countAs
     *
     * @param {string[]} levels RIP levels in JSON String format
     * @returns {any[]} Merged levels 
     */
    mergeLevels(levels) {
      const baseLevel = JSON.parse(levels[0]);
      const /** @type {{[key:string]: any}} */ ripImportData = {
        sku: baseLevel.sku,
        ripCode: baseLevel.ripCode,
        brandRegistration: null,
        fromDate: baseLevel.fromDate,
        toDate: baseLevel.toDate,
        description: baseLevel.description,
        uom1: baseLevel.uom,
        qty1: baseLevel.qty,
        amt1: baseLevel.amt,
      };
      
      for (let i = 1; i < 5; i++) {
        const level = i < levels.length ? JSON.parse(levels[i]) : {};
        ripImportData[`uom${i+1}`] = level.uom;
        ripImportData[`qty${i+1}`] = level.qty || "0";
        ripImportData[`amt${i+1}`] = level.amt || "0";
      }

      ripImportData.countAs = parseFloat(baseLevel.countAs);
      ripImportData.comments = baseLevel.comments;
      ripImportData.lineCount = baseLevel.lineCount;

      return Object.values(ripImportData);
    }
  }

  exports.BridgeRIPFedway = BridgeRIPFedway;
});