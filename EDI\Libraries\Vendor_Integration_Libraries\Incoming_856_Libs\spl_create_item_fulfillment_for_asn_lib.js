/**
 * @NApiVersion 2.1
 */

//Called By: Process Incoming 856 lib
//Function: Create an IF for the SO with the correct items and quantities

define(["N/log", "N/record", "N/search"], function (log, record, search) {
  function createItemFulfillment(
    asnItems,
    netSuiteCarrierId,
    trackingNumber,
    transactionControlNumber,
    salesOrderInternalId
  ) {
    var helperFunctions = (function () {
      function transformSalesOrder(salesOrderInternalId) {
        try {
          return record.transform({
            fromType: record.Type.SALES_ORDER,
            fromId: salesOrderInternalId,
            toType: record.Type.ITEM_FULFILLMENT,
          });
        } catch (e) {
          throw `Item Fulfillment Record Not Created: ${e.message}`;
        }
      }

      /**
       * Retrieves a list of inventory items on the newly created IF record
       *
       * @returns {Array<Object>} Array of objects containing item details:
       *   @returns {string} itemName - The item ID/name
       *   @returns {number} quantity - The item quantity
       *   @returns {string} vendorname - The vendor name associated with the item
       *   @returns {number} lineNumber - The line number in the sublist
       * @throws {string} Error message if items cannot be retrieved
       */
      function getItemsOnItemFulfillment() {
        try {
          if (!itemFulfillmentRecord) {
            throw "Item fulfillment record not available";
          }

          const itemCount = itemFulfillmentRecord.getLineCount({
            sublistId: "item",
          });

          const results = [];

          for (let i = 0; i < itemCount; i++) {
            const itemId = itemFulfillmentRecord.getSublistValue({
              sublistId: "item",
              fieldId: "item",
              line: i,
            });

            const itemLookup = search.lookupFields({
              type: search.Type.ITEM,
              id: itemId,
              columns: ["itemid", "vendorname"],
            });

            results.push({
              itemName: itemLookup.itemid,
              quantity: itemFulfillmentRecord.getSublistValue({
                sublistId: "item",
                fieldId: "quantity",
                line: i,
              }),
              vendorname: itemLookup.vendorname,
              lineNumber: i,
            });
          }

          if (results.length <= 0) {
            throw "No items found on item fulfillment record";
          }

          return results;
        } catch (err) {
          throw `Items Not Gotten Correctly: ${err}`;
        }
      }

      function updateItems(netSuiteIfItemsArr, asnItems) {
        try {
          netSuiteIfItemsArr.forEach((netSuiteItem) => {
            var asnItem = asnItems.find((toFind) => {
              return (
                netSuiteItem.itemName == toFind.itemName ||
                netSuiteItem.vendorname == toFind.itemName
              );
            });

            //Removed item from fulfillment if not found on EDI file
            if (!asnItem) {
              itemFulfillmentRecord.setSublistValue({
                sublistId: "item",
                fieldId: "itemreceive",
                line: netSuiteItem.lineNumber,
                value: false,
              });
              processingLog.push(
                `${netSuiteItem.itemName} in NS is not on the EDI file sent, it wasn't added to the item fulfillment`
              );
            } else {
              //If found, compare quantities and adjust if needed
              if (netSuiteItem.quantity !== asnItem.quantity) {
                processingLog.push(
                  `The quantity shipped for ${netSuiteItem.itemName} (${asnItem.quantity}) is different than what is on the sales order (${netSuiteItem.quantity})`
                );

                itemFulfillmentRecord.setSublistValue({
                  sublistId: "item",
                  fieldId: "quantity",
                  line: netSuiteItem.lineNumber,
                  value: asnItem.quantity,
                });
              }
            }
          });

          //Find any items on EDI file, not on record in NS.
          const itemsMissingInNetSuiteArr = asnItems.filter((asnItem) => {
            /*If found in NS items, return false, item is not missing in NS 
						-> don't include in returned array*/

            return !netSuiteIfItemsArr.some((netSuiteItem) => {
              return (
                netSuiteItem.itemName === asnItem.itemName ||
                netSuiteItem.vendorname === asnItem.itemName
              );
            });
          });

          itemsMissingInNetSuiteArr.forEach((missingItem) => {
            processingLog.push(
              `${missingItem.itemName} was sent on the EDI file but wasn't found on the order in NS`
            );
          });
        } catch (e) {
          throw `Error Comparing and Updating Items: ${e}`;
        }
      }

      function setMainValues() {
        itemFulfillmentRecord.setValue(
          "custbody_spl_shipping_carrier",
          netSuiteCarrierId
        );

        itemFulfillmentRecord.setValue(
          "custbody_spl_tracking_information",
          trackingNumber
        );

        itemFulfillmentRecord.setValue(
          "custbody_spl_asn_edi_trnsctn_cntrl_nbr",
          transactionControlNumber
        );
      }

      return {
        transformSalesOrder,
        getItemsOnItemFulfillment,
        updateItems,
        setMainValues,
      };
    })();

    const errorLog = [];
    const processingLog = [];

    let itemFulfillmentInternalId = null;
    let itemFulfillmentName = "To Be Generated";

    const itemFulfillmentRecord =
      helperFunctions.transformSalesOrder(salesOrderInternalId);

    try {
      const netSuiteIfItemsArr = helperFunctions.getItemsOnItemFulfillment();

      helperFunctions.updateItems(netSuiteIfItemsArr, asnItems);
      helperFunctions.setMainValues();
      itemFulfillmentInternalId = itemFulfillmentRecord.save();
      itemFulfillmentName = itemFulfillmentRecord.getValue("tranid");
    } catch (e) {
      errorLog.push(e);
    }

    return {
      errorLog,
      processingLog,
      itemFulfillmentInternalId,
      itemFulfillmentName,
    };
  }

  return {
    createItemFulfillment,
  };
});
