/**
 * Create the Purchase Order once an EDI Sales Order is approved
 *
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig /SuiteScripts/config.json
 * <AUTHOR>
 * @module spl_create_purchase_order_ue
 */

define([
  "N/log",
  "N/search",
  "N/record",
  "N/error",
  "CreatePurchaseOrderLib",
], function (log, search, record, error, createPurchaseOrderLib) {
  function afterSubmit(context) {
    const { newRecord, oldRecord, type } = context;

    try {
      const purchaseOrderObj = {
        items: [],
      };

      const dropShipCustomerGroupsObj = {
        427: "0018 Stern Rehab",
        452: "0043 Crown Healthcare Group",
        545: "0119 Empire Care Centers", //--added by Ero LDEV-3388
        2849: "1836 Recover Care",
        //3155: "2032 CareRite Services LLC", --removed by Ero LDEV-3388
        4302: "2759 Infinite Care",
        7460: "3454 CareCore Health",
        //17554: "5104 Venza", --removed by Ero LDEV-3388
        177115: "168170 Global Distributors",
        454: "0045 Emerald Healthcare",
        414: "0005 Outcome Healthcare",
        86861: "165996 EOM Healthcare",
        //444: "0035 Caretech Group", --removed by Ero LDEV-3388
        532: "0106 Pavilion Healthcare Group",
        3980: "2371 Apex Global Solutions",
        517: "0091 Topaz Financial Services",
        8141: "3617 Lofts",
        6851: "3286 Ventura Services",
        340927: "171340 Key Purchasing",
      };

      const dropShipStates = [
        "FL", //Florida
        "CO", //Colorado
        "IL", //Illinois
        "IN", //Indiana
        "KS", //Kansas
        "OH", //Ohio
        "TN", //Tennessee
        "MI", //Michigan
        "KY", //Kentucky
        "AL", //Alabama
        "GA", //Georgia
        "SC", //South Carolina
        "NC", //North Carolina
        "NE", //Nebraska
        "OK", //Oklahoma
        "WI", //Wisconsin
        "CA", //California
      ];

      const gloveItemsObj = {
        54939: "PPE1117",
        54942: "PPE1117L",
        54941: "PPE1117M",
        54940: "PPE1117S",
        54943: "PPE1117XL",
        54946: "PPE1119",
        // 54949: "PPE1119L",
        54948: "PPE1119M",
        54947: "PPE1119S",
        //54950: "PPE1119XL",
        54841: "PPE1100",
      };

      const pendingApprovalStatusId = "A";
      const pendingFulfillmentStatusId = "B";

      const oldStatus =
        type === context.UserEventType.EDIT
          ? oldRecord.getValue({
              fieldId: "orderstatus",
            })
          : null;

      const newStatus = newRecord.getValue({
        fieldId: "orderstatus",
      });

      // Allow the PO creation if
      // - the Sales Order has been approved
      // - the Sales Order status changed from Pending Approval to Pending Fulfillment
      // - the Sales Order has been created with a Pending Fulfillment status
      if (
        type === context.UserEventType.APPROVE ||
        ((type === context.UserEventType.CREATE ||
          oldStatus === pendingApprovalStatusId) &&
          newStatus === pendingFulfillmentStatusId)
      ) {
        const customerId = newRecord.getValue({ fieldId: "entity" });
        const customerLookup = search.lookupFields({
          type: search.Type.CUSTOMER,
          id: customerId,
          columns: ["parent"],
        });

        const parentInternalId =
          customerLookup &&
          customerLookup["parent"] &&
          Array.isArray(customerLookup["parent"]) &&
          customerLookup["parent"].length > 0 &&
          customerLookup["parent"][0].value;

        if (
          !Object.keys(dropShipCustomerGroupsObj).includes(parentInternalId)
        ) {
          return;
        }

        purchaseOrderObj.customerInternalId = customerId;

        const shippingAddress = newRecord.getSubrecord({
          fieldId: "shippingaddress",
        });

        const state = shippingAddress
          ? shippingAddress.getValue({
              fieldId: "state",
            })
          : "";

          //When is multi-line shipping - when and how are ship addresses set? 
        if (state === "") {
          throw error.create({
            name: "ERROR_RETRIEVING_STATE",
            message: `Cannot retrieve the state assigned to the shipping address of the item. Shipping Address: ${JSON.stringify(
              shippingAddress
            )}`,
          });
        }

        if (!dropShipStates.includes(state)) {
          return;
        }

        purchaseOrderObj.shippingAddress = newRecord.getValue("shipaddress");

        const lineCount = newRecord.getLineCount({ sublistId: "item" });

        for (let line = 0; line < lineCount; line++) {
          const internalId = newRecord.getSublistValue({
            sublistId: "item",
            fieldId: "item",
            line,
          });

          if (!Object.keys(gloveItemsObj).includes(internalId)) {
            continue;
          }

          let itemName = "";

          try {
            itemName = newRecord.getSublistText({
              sublistId: "item",
              fieldId: "item",
              line,
            });
          } catch (err) {
            log.audit(
              "WARNING",
              "Error getting item name. Using internalId instead."
            );
            itemName = `Item with internalId ${internalId}`;
          }

          const quantity = newRecord.getSublistValue({
            sublistId: "item",
            fieldId: "quantity",
            line,
          });

          const uomId = newRecord.getSublistValue({
            sublistId: "item",
            fieldId: "units",
            line,
          });

          const vendorCost = newRecord.getSublistValue({
            sublistId: "item",
            fieldId: "porate",
            line,
          });

          purchaseOrderObj.items.push({
            internalId,
            itemName,
            quantity,
            uomId,
            vendorCost,
            line,
          });
        }

        if (purchaseOrderObj.items.length > 0) {
          const { processingErrorsArr } =
            createPurchaseOrderLib.createPoWithDropShipItems(
              purchaseOrderObj,
              newRecord.id
            );

          if (processingErrorsArr && processingErrorsArr.length > 0) {
            log.error(
              "Error while creating Purchase Order",
              processingErrorsArr
            );
          } else {
            //If no errors creating PO, set SO item location to be drop ship
            const salesOrderRecord = record.load({
              type: record.Type.SALES_ORDER,
              id: newRecord.id,
            });

            purchaseOrderObj.items.forEach((item) => {
              let lineIndex = salesOrderRecord.findSublistLineWithValue({
                sublistId: "item",
                fieldId: "item",
                value: item.internalId,
              });

              salesOrderRecord.setSublistValue({
                sublistId: "item",
                fieldId: "location",
                line: lineIndex,
                value: 4,
              });
            });

            salesOrderRecord.save();
          }
        }
      }
    } catch (/** @type {any} */ err) {
      log.error(
        err.name,
        `Internal ID: ${newRecord.id}\nError: ${err.message}`
      );
    }
  }

  return {
    afterSubmit,
  };
});
