/**
 * @description Send out emails of 
 * 
 * </br><b>Schedule:</b> Every Friday
 *
 * @NApiVersion 2.1
 * @NScriptType ScheduledScript
 * 
 * <AUTHOR>
 * @module vlmd_wms_unassociate_bins_mr
 */
define([
	"require",
	"N/email",
	"N/query",
	"N/runtime",
	"../Classes/vlmd_custom_error_object",
	"../Classes/vlmd_mr_summary_handling",
],(require) => {
        const record = require("N/email");
        const query = require("N/query");
        const runtime = require("N/runtime");
		
        const CustomErrorObject = require("../Classes/vlmd_custom_error_object");
        const customErrorObject = new CustomErrorObject();
		
	function execute(context) {
		// Get all unassociated bins from last week that still have 0 quantity
		let sqlQuery = /*sql*/ `
            SELECT
                event_log.custrecord_spl_biel_bin AS bin,
                event_log.custrecord_spl_biel_item AS item,
                event_log.custrecord_spl_biel_event_timestamp AS timestamp,
                event_log.custrecord_spl_biel_event_type AS event_type,
                COALESCE(item_bin_qty.quantityonhand, 0) AS current_quantity
            FROM
                customrecord_spl_bin_item_event_log AS event_log
			LEFT JOIN
				itembinquantity AS item_bin_qty
				ON event_log.custrecord_spl_biel_bin = item_bin_qty.bin
				AND event_log.custrecord_spl_biel_item = item_bin_qty.item
            WHERE
                event_log.custrecord_spl_biel_event_type = 'Unassociated' AND
                event_log.custrecord_spl_biel_event_timestamp BETWEEN
                    TO_DATE(BUILTIN.RELATIVE_RANGES('LBW','START'), 'MM/DD/YYYY') AND
                    TO_DATE(BUILTIN.RELATIVE_RANGES('LBW','END'), 'MM/DD/YYYY') AND
                COALESCE(item_bin_qty.quantityonhand, 0) = 0
		`;

		let unassociatedBinsFromLastWeek = query.runSuiteQL({
			query: sqlQuery,
		})?.asMappedResults();

	}

	return {
		execute,
	};
});
